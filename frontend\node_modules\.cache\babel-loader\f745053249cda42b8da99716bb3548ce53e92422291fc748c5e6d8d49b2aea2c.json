{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Companion\\\\frontend\\\\src\\\\components\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Users, Heart, Activity, TrendingUp, UserCheck, UserX } from 'lucide-react';\nimport ManageUsers from './ManageUsers';\nimport './AdminDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = ({\n  user\n}) => {\n  _s();\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    totalCaregivers: 0,\n    activeRequests: 0,\n    completedServices: 0\n  });\n  const [recentUsers, setRecentUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchAdminStats();\n    fetchRecentUsers();\n  }, []);\n  const fetchAdminStats = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/admin/stats', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setStats(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching admin stats:', error);\n    }\n  };\n  const fetchRecentUsers = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/admin/recent-users?limit=5', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setRecentUsers(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching recent users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleUserStatusUpdate = async (userId, isVerified) => {\n    try {\n      const token = localStorage.getItem('token');\n      await axios.patch(`http://localhost:5000/api/admin/users/${userId}/status`, {\n        isVerified\n      }, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n\n      // Refresh the users list\n      fetchRecentUsers();\n      fetchAdminStats();\n    } catch (error) {\n      console.error('Error updating user status:', error);\n      alert('Failed to update user status');\n    }\n  };\n  const StatCard = ({\n    icon,\n    title,\n    value,\n    color\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"stat-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `stat-icon ${color}`,\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"stat-value\",\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"stat-title\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-dashboard\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"dashboard-title\",\n          children: \"Admin Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"dashboard-subtitle\",\n          children: [\"Welcome back, \", user.firstName, \"! Here's what's happening in your community.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          icon: /*#__PURE__*/_jsxDEV(Users, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 19\n          }, this),\n          title: \"Total Users\",\n          value: stats.totalUsers,\n          color: \"blue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          icon: /*#__PURE__*/_jsxDEV(Heart, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 19\n          }, this),\n          title: \"Caregivers\",\n          value: stats.totalCaregivers,\n          color: \"pink\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          icon: /*#__PURE__*/_jsxDEV(Activity, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 19\n          }, this),\n          title: \"Active Requests\",\n          value: stats.activeRequests,\n          color: \"orange\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 19\n          }, this),\n          title: \"Completed Services\",\n          value: stats.completedServices,\n          color: \"green\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Recent Users\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-info\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"User Verification System:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 16\n            }, this), \" New users must be approved by admin before they can access the platform. Pending users can register but cannot login until verified.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading users...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-container\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"users-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: recentUsers.length > 0 ? recentUsers.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"user-name\",\n                  children: user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"user-email\",\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `role-badge ${user.role}`,\n                    children: user.role\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `status-badge ${user.status}`,\n                    children: user.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"user-actions\",\n                  children: user.status === 'pending' ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn approve\",\n                    onClick: () => handleUserStatusUpdate(user.id, true),\n                    title: \"Approve User\",\n                    children: /*#__PURE__*/_jsxDEV(UserCheck, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn reject\",\n                    onClick: () => handleUserStatusUpdate(user.id, false),\n                    title: \"Suspend User\",\n                    children: /*#__PURE__*/_jsxDEV(UserX, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 25\n                }, this)]\n              }, user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 23\n              }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: \"5\",\n                  className: \"no-data\",\n                  children: \"No users found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quick-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(Users, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Manage Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(Heart, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Review Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(Activity, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"View Reports\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"mLTlOiLoMA7Yag+JFqO4tg/CCak=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["useState", "useEffect", "axios", "Users", "Heart", "Activity", "TrendingUp", "UserCheck", "UserX", "ManageUsers", "jsxDEV", "_jsxDEV", "AdminDashboard", "user", "_s", "stats", "setStats", "totalUsers", "totalCaregivers", "activeRequests", "completedServices", "recentUsers", "setRecentUsers", "loading", "setLoading", "fetchAdminStats", "fetchRecentUsers", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "data", "success", "error", "console", "handleUserStatusUpdate", "userId", "isVerified", "patch", "alert", "StatCard", "icon", "title", "value", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "size", "length", "map", "name", "email", "role", "status", "onClick", "id", "colSpan", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Companion/frontend/src/components/AdminDashboard.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Users, Heart, Activity, TrendingUp, UserCheck, UserX } from 'lucide-react';\nimport ManageUsers from './ManageUsers';\nimport './AdminDashboard.css';\n\nconst AdminDashboard = ({ user }) => {\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    totalCaregivers: 0,\n    activeRequests: 0,\n    completedServices: 0\n  });\n  const [recentUsers, setRecentUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchAdminStats();\n    fetchRecentUsers();\n  }, []);\n\n  const fetchAdminStats = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/admin/stats', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setStats(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching admin stats:', error);\n    }\n  };\n\n  const fetchRecentUsers = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/admin/recent-users?limit=5', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setRecentUsers(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching recent users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleUserStatusUpdate = async (userId, isVerified) => {\n    try {\n      const token = localStorage.getItem('token');\n      await axios.patch(`http://localhost:5000/api/admin/users/${userId}/status`,\n        { isVerified },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n\n      // Refresh the users list\n      fetchRecentUsers();\n      fetchAdminStats();\n    } catch (error) {\n      console.error('Error updating user status:', error);\n      alert('Failed to update user status');\n    }\n  };\n\n\n\n  const StatCard = ({ icon, title, value, color }) => (\n    <div className=\"stat-card\">\n      <div className={`stat-icon ${color}`}>\n        {icon}\n      </div>\n      <div className=\"stat-content\">\n        <h3 className=\"stat-value\">{value}</h3>\n        <p className=\"stat-title\">{title}</p>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"admin-dashboard\">\n      <div className=\"dashboard-container\">\n        <div className=\"dashboard-header\">\n          <h1 className=\"dashboard-title\">Admin Dashboard</h1>\n          <p className=\"dashboard-subtitle\">\n            Welcome back, {user.firstName}! Here's what's happening in your community.\n          </p>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"stats-grid\">\n          <StatCard\n            icon={<Users size={24} />}\n            title=\"Total Users\"\n            value={stats.totalUsers}\n            color=\"blue\"\n          />\n          <StatCard\n            icon={<Heart size={24} />}\n            title=\"Caregivers\"\n            value={stats.totalCaregivers}\n            color=\"pink\"\n          />\n          <StatCard\n            icon={<Activity size={24} />}\n            title=\"Active Requests\"\n            value={stats.activeRequests}\n            color=\"orange\"\n          />\n          <StatCard\n            icon={<TrendingUp size={24} />}\n            title=\"Completed Services\"\n            value={stats.completedServices}\n            color=\"green\"\n          />\n        </div>\n\n        {/* Recent Users Table */}\n        <div className=\"dashboard-section\">\n          <h2 className=\"section-title\">Recent Users</h2>\n          <div className=\"section-info\">\n            <p><strong>User Verification System:</strong> New users must be approved by admin before they can access the platform.\n            Pending users can register but cannot login until verified.</p>\n          </div>\n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"loading-spinner\"></div>\n              <p>Loading users...</p>\n            </div>\n          ) : (\n            <div className=\"table-container\">\n              <table className=\"users-table\">\n                <thead>\n                  <tr>\n                    <th>Name</th>\n                    <th>Email</th>\n                    <th>Role</th>\n                    <th>Status</th>\n                    <th>Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {recentUsers.length > 0 ? (\n                    recentUsers.map(user => (\n                      <tr key={user.id}>\n                        <td className=\"user-name\">{user.name}</td>\n                        <td className=\"user-email\">{user.email}</td>\n                        <td>\n                          <span className={`role-badge ${user.role}`}>\n                            {user.role}\n                          </span>\n                        </td>\n                        <td>\n                          <span className={`status-badge ${user.status}`}>\n                            {user.status}\n                          </span>\n                        </td>\n                        <td className=\"user-actions\">\n                          {user.status === 'pending' ? (\n                            <button\n                              className=\"action-btn approve\"\n                              onClick={() => handleUserStatusUpdate(user.id, true)}\n                              title=\"Approve User\"\n                            >\n                              <UserCheck size={16} />\n                            </button>\n                          ) : (\n                            <button\n                              className=\"action-btn reject\"\n                              onClick={() => handleUserStatusUpdate(user.id, false)}\n                              title=\"Suspend User\"\n                            >\n                              <UserX size={16} />\n                            </button>\n                          )}\n                        </td>\n                      </tr>\n                    ))\n                  ) : (\n                    <tr>\n                      <td colSpan=\"5\" className=\"no-data\">\n                        No users found\n                      </td>\n                    </tr>\n                  )}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"dashboard-section\">\n          <h2 className=\"section-title\">Quick Actions</h2>\n          <div className=\"quick-actions\">\n            <button className=\"action-card\">\n              <Users size={24} />\n              <span>Manage Users</span>\n            </button>\n            <button className=\"action-card\">\n              <Heart size={24} />\n              <span>Review Services</span>\n            </button>\n            <button className=\"action-card\">\n              <Activity size={24} />\n              <span>View Reports</span>\n            </button>\n            <button className=\"action-card\">\n              <TrendingUp size={24} />\n              <span>Analytics</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,QAAQ,cAAc;AACnF,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACnC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC;IACjCiB,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE,CAAC;IACjBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdwB,eAAe,CAAC,CAAC;IACjBC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM5B,KAAK,CAAC6B,GAAG,CAAC,uCAAuC,EAAE;QACxEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBnB,QAAQ,CAACc,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMV,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM5B,KAAK,CAAC6B,GAAG,CAAC,sDAAsD,EAAE;QACvFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBb,cAAc,CAACQ,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;MACpC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,sBAAsB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,UAAU,KAAK;IAC3D,IAAI;MACF,MAAMb,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAM3B,KAAK,CAACuC,KAAK,CAAC,yCAAyCF,MAAM,SAAS,EACxE;QAAEC;MAAW,CAAC,EACd;QAAER,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAAE,CAClD,CAAC;;MAED;MACAD,gBAAgB,CAAC,CAAC;MAClBD,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDM,KAAK,CAAC,8BAA8B,CAAC;IACvC;EACF,CAAC;EAID,MAAMC,QAAQ,GAAGA,CAAC;IAAEC,IAAI;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAM,CAAC,kBAC7CpC,OAAA;IAAKqC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBtC,OAAA;MAAKqC,SAAS,EAAE,aAAaD,KAAK,EAAG;MAAAE,QAAA,EAClCL;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACN1C,OAAA;MAAKqC,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BtC,OAAA;QAAIqC,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEH;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvC1C,OAAA;QAAGqC,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEJ;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE1C,OAAA;IAAKqC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BtC,OAAA;MAAKqC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCtC,OAAA;QAAKqC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BtC,OAAA;UAAIqC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpD1C,OAAA;UAAGqC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAC,gBAClB,EAACpC,IAAI,CAACyC,SAAS,EAAC,8CAChC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN1C,OAAA;QAAKqC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBtC,OAAA,CAACgC,QAAQ;UACPC,IAAI,eAAEjC,OAAA,CAACR,KAAK;YAACoD,IAAI,EAAE;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BR,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAE/B,KAAK,CAACE,UAAW;UACxB8B,KAAK,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACF1C,OAAA,CAACgC,QAAQ;UACPC,IAAI,eAAEjC,OAAA,CAACP,KAAK;YAACmD,IAAI,EAAE;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BR,KAAK,EAAC,YAAY;UAClBC,KAAK,EAAE/B,KAAK,CAACG,eAAgB;UAC7B6B,KAAK,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACF1C,OAAA,CAACgC,QAAQ;UACPC,IAAI,eAAEjC,OAAA,CAACN,QAAQ;YAACkD,IAAI,EAAE;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BR,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAE/B,KAAK,CAACI,cAAe;UAC5B4B,KAAK,EAAC;QAAQ;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACF1C,OAAA,CAACgC,QAAQ;UACPC,IAAI,eAAEjC,OAAA,CAACL,UAAU;YAACiD,IAAI,EAAE;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC/BR,KAAK,EAAC,oBAAoB;UAC1BC,KAAK,EAAE/B,KAAK,CAACK,iBAAkB;UAC/B2B,KAAK,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN1C,OAAA;QAAKqC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtC,OAAA;UAAIqC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/C1C,OAAA;UAAKqC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BtC,OAAA;YAAAsC,QAAA,gBAAGtC,OAAA;cAAAsC,QAAA,EAAQ;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,yIACc;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,EACL9B,OAAO,gBACNZ,OAAA;UAAKqC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BtC,OAAA;YAAKqC,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvC1C,OAAA;YAAAsC,QAAA,EAAG;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,gBAEN1C,OAAA;UAAKqC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BtC,OAAA;YAAOqC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC5BtC,OAAA;cAAAsC,QAAA,eACEtC,OAAA;gBAAAsC,QAAA,gBACEtC,OAAA;kBAAAsC,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb1C,OAAA;kBAAAsC,QAAA,EAAI;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd1C,OAAA;kBAAAsC,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb1C,OAAA;kBAAAsC,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACf1C,OAAA;kBAAAsC,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR1C,OAAA;cAAAsC,QAAA,EACG5B,WAAW,CAACmC,MAAM,GAAG,CAAC,GACrBnC,WAAW,CAACoC,GAAG,CAAC5C,IAAI,iBAClBF,OAAA;gBAAAsC,QAAA,gBACEtC,OAAA;kBAAIqC,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEpC,IAAI,CAAC6C;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1C1C,OAAA;kBAAIqC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEpC,IAAI,CAAC8C;gBAAK;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5C1C,OAAA;kBAAAsC,QAAA,eACEtC,OAAA;oBAAMqC,SAAS,EAAE,cAAcnC,IAAI,CAAC+C,IAAI,EAAG;oBAAAX,QAAA,EACxCpC,IAAI,CAAC+C;kBAAI;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL1C,OAAA;kBAAAsC,QAAA,eACEtC,OAAA;oBAAMqC,SAAS,EAAE,gBAAgBnC,IAAI,CAACgD,MAAM,EAAG;oBAAAZ,QAAA,EAC5CpC,IAAI,CAACgD;kBAAM;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL1C,OAAA;kBAAIqC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EACzBpC,IAAI,CAACgD,MAAM,KAAK,SAAS,gBACxBlD,OAAA;oBACEqC,SAAS,EAAC,oBAAoB;oBAC9Bc,OAAO,EAAEA,CAAA,KAAMxB,sBAAsB,CAACzB,IAAI,CAACkD,EAAE,EAAE,IAAI,CAAE;oBACrDlB,KAAK,EAAC,cAAc;oBAAAI,QAAA,eAEpBtC,OAAA,CAACJ,SAAS;sBAACgD,IAAI,EAAE;oBAAG;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,gBAET1C,OAAA;oBACEqC,SAAS,EAAC,mBAAmB;oBAC7Bc,OAAO,EAAEA,CAAA,KAAMxB,sBAAsB,CAACzB,IAAI,CAACkD,EAAE,EAAE,KAAK,CAAE;oBACtDlB,KAAK,EAAC,cAAc;oBAAAI,QAAA,eAEpBtC,OAAA,CAACH,KAAK;sBAAC+C,IAAI,EAAE;oBAAG;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GA/BExC,IAAI,CAACkD,EAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgCZ,CACL,CAAC,gBAEF1C,OAAA;gBAAAsC,QAAA,eACEtC,OAAA;kBAAIqD,OAAO,EAAC,GAAG;kBAAChB,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAEpC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN1C,OAAA;QAAKqC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtC,OAAA;UAAIqC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChD1C,OAAA;UAAKqC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BtC,OAAA;YAAQqC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7BtC,OAAA,CAACR,KAAK;cAACoD,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnB1C,OAAA;cAAAsC,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACT1C,OAAA;YAAQqC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7BtC,OAAA,CAACP,KAAK;cAACmD,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnB1C,OAAA;cAAAsC,QAAA,EAAM;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACT1C,OAAA;YAAQqC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7BtC,OAAA,CAACN,QAAQ;cAACkD,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtB1C,OAAA;cAAAsC,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACT1C,OAAA;YAAQqC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7BtC,OAAA,CAACL,UAAU;cAACiD,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxB1C,OAAA;cAAAsC,QAAA,EAAM;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CAvNIF,cAAc;AAAAqD,EAAA,GAAdrD,cAAc;AAyNpB,eAAeA,cAAc;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}