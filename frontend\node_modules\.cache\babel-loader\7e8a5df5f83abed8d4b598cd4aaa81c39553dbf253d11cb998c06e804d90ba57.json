{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Companion\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { useState, useEffect } from 'react';\nimport Navbar from './components/Navbar';\nimport LandingPage from './components/LandingPage';\nimport Login from './components/Login';\nimport Signup from './components/Signup';\nimport AdminLogin from './components/AdminLogin';\nimport AdminDashboard from './components/AdminDashboard';\nimport UserDashboard from './components/UserDashboard';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Check if user is logged in on app start\n    const token = localStorage.getItem('token');\n    const userData = localStorage.getItem('user');\n    if (token && userData) {\n      try {\n        setUser(JSON.parse(userData));\n      } catch (error) {\n        console.error('Error parsing user data:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n  const handleLogin = (userData, token) => {\n    setUser(userData);\n    localStorage.setItem('token', token);\n    localStorage.setItem('user', JSON.stringify(userData));\n  };\n  const handleLogout = () => {\n    setUser(null);\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        user: user,\n        onLogout: handleLogout\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(AdminLogin, {\n            onLogin: handleLogin\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/user-login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {\n            onLogin: handleLogin\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/signup\",\n          element: /*#__PURE__*/_jsxDEV(Signup, {\n            onLogin: handleLogin\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: user && user.role === 'admin' ? /*#__PURE__*/_jsxDEV(AdminDashboard, {\n            user: user\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"unauthorized\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Unauthorized Access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"You need admin privileges to access this page.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "LandingPage", "<PERSON><PERSON>", "Signup", "AdminLogin", "AdminDashboard", "UserDashboard", "jsxDEV", "_jsxDEV", "App", "_s", "user", "setUser", "loading", "setLoading", "token", "localStorage", "getItem", "userData", "JSON", "parse", "error", "console", "removeItem", "handleLogin", "setItem", "stringify", "handleLogout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onLogout", "path", "element", "onLogin", "role", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Companion/frontend/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { useState, useEffect } from 'react';\nimport Navbar from './components/Navbar';\nimport LandingPage from './components/LandingPage';\nimport Login from './components/Login';\nimport Signup from './components/Signup';\nimport AdminLogin from './components/AdminLogin';\nimport AdminDashboard from './components/AdminDashboard';\nimport UserDashboard from './components/UserDashboard';\nimport './App.css';\n\nfunction App() {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is logged in on app start\n    const token = localStorage.getItem('token');\n    const userData = localStorage.getItem('user');\n\n    if (token && userData) {\n      try {\n        setUser(JSON.parse(userData));\n      } catch (error) {\n        console.error('Error parsing user data:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  const handleLogin = (userData, token) => {\n    setUser(userData);\n    localStorage.setItem('token', token);\n    localStorage.setItem('user', JSON.stringify(userData));\n  };\n\n  const handleLogout = () => {\n    setUser(null);\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"loading-container\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading...</p>\n      </div>\n    );\n  }\n\n  return (\n    <Router>\n      <div className=\"App\">\n        <Navbar user={user} onLogout={handleLogout} />\n        <Routes>\n          <Route path=\"/\" element={<LandingPage />} />\n          <Route path=\"/login\" element={<AdminLogin onLogin={handleLogin} />} />\n          <Route path=\"/user-login\" element={<Login onLogin={handleLogin} />} />\n          <Route path=\"/signup\" element={<Signup onLogin={handleLogin} />} />\n          <Route\n            path=\"/admin\"\n            element={\n              user && user.role === 'admin' ?\n                <AdminDashboard user={user} /> :\n                <div className=\"unauthorized\">\n                  <h2>Unauthorized Access</h2>\n                  <p>You need admin privileges to access this page.</p>\n                </div>\n            }\n          />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE7C,IAAIF,KAAK,IAAIG,QAAQ,EAAE;MACrB,IAAI;QACFN,OAAO,CAACO,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,CAAC;MAC/B,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDL,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;QAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IACAT,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,WAAW,GAAGA,CAACN,QAAQ,EAAEH,KAAK,KAAK;IACvCH,OAAO,CAACM,QAAQ,CAAC;IACjBF,YAAY,CAACS,OAAO,CAAC,OAAO,EAAEV,KAAK,CAAC;IACpCC,YAAY,CAACS,OAAO,CAAC,MAAM,EAAEN,IAAI,CAACO,SAAS,CAACR,QAAQ,CAAC,CAAC;EACxD,CAAC;EAED,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzBf,OAAO,CAAC,IAAI,CAAC;IACbI,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;IAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAED,IAAIV,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKoB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCrB,OAAA;QAAKoB,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCzB,OAAA;QAAAqB,QAAA,EAAG;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAEV;EAEA,oBACEzB,OAAA,CAACb,MAAM;IAAAkC,QAAA,eACLrB,OAAA;MAAKoB,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBrB,OAAA,CAACR,MAAM;QAACW,IAAI,EAAEA,IAAK;QAACuB,QAAQ,EAAEP;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9CzB,OAAA,CAACZ,MAAM;QAAAiC,QAAA,gBACLrB,OAAA,CAACX,KAAK;UAACsC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAE5B,OAAA,CAACP,WAAW;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5CzB,OAAA,CAACX,KAAK;UAACsC,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAE5B,OAAA,CAACJ,UAAU;YAACiC,OAAO,EAAEb;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtEzB,OAAA,CAACX,KAAK;UAACsC,IAAI,EAAC,aAAa;UAACC,OAAO,eAAE5B,OAAA,CAACN,KAAK;YAACmC,OAAO,EAAEb;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtEzB,OAAA,CAACX,KAAK;UAACsC,IAAI,EAAC,SAAS;UAACC,OAAO,eAAE5B,OAAA,CAACL,MAAM;YAACkC,OAAO,EAAEb;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnEzB,OAAA,CAACX,KAAK;UACJsC,IAAI,EAAC,QAAQ;UACbC,OAAO,EACLzB,IAAI,IAAIA,IAAI,CAAC2B,IAAI,KAAK,OAAO,gBAC3B9B,OAAA,CAACH,cAAc;YAACM,IAAI,EAAEA;UAAK;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAC9BzB,OAAA;YAAKoB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrB,OAAA;cAAAqB,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BzB,OAAA;cAAAqB,QAAA,EAAG;YAA8C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACvB,EAAA,CAlEQD,GAAG;AAAA8B,EAAA,GAAH9B,GAAG;AAoEZ,eAAeA,GAAG;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}