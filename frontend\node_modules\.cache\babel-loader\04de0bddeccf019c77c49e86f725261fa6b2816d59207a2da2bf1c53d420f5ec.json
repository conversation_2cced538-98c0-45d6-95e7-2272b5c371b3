{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Companion\\\\frontend\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Heart, User, LogOut, Shield } from 'lucide-react';\nimport './Navbar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = ({\n  user,\n  onLogout\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    onLogout();\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-container\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"navbar-brand\",\n        children: [/*#__PURE__*/_jsxDEV(Heart, {\n          className: \"brand-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Companion\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navbar-menu\",\n        children: user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navbar-user\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-greeting\",\n            children: [\"Hello, \", user.firstName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 15\n          }, this), user.role === 'admin' && /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/admin\",\n            className: \"navbar-link admin-link\",\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 19\n            }, this), \"Admin Dashboard\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-menu\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"user-button\",\n              children: [/*#__PURE__*/_jsxDEV(User, {\n                size: 18\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: user.firstName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-dropdown\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/profile\",\n                className: \"dropdown-item\",\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 21\n                }, this), \"Profile\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleLogout,\n                className: \"dropdown-item logout-btn\",\n                children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 21\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navbar-auth\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"navbar-link admin-login\",\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 17\n            }, this), \"Admin Login\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["Link", "useNavigate", "Heart", "User", "LogOut", "Shield", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "user", "onLogout", "_s", "navigate", "handleLogout", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "role", "size", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Companion/frontend/src/components/Navbar.js"], "sourcesContent": ["import { Link, useNavigate } from 'react-router-dom';\nimport { Heart, User, LogOut, Shield } from 'lucide-react';\nimport './Navbar.css';\n\nconst Navbar = ({ user, onLogout }) => {\n  const navigate = useNavigate();\n\n  const handleLogout = () => {\n    onLogout();\n    navigate('/');\n  };\n\n  return (\n    <nav className=\"navbar\">\n      <div className=\"navbar-container\">\n        <Link to=\"/\" className=\"navbar-brand\">\n          <Heart className=\"brand-icon\" />\n          <span>Companion</span>\n        </Link>\n\n        <div className=\"navbar-menu\">\n          {user ? (\n            <div className=\"navbar-user\">\n              <span className=\"user-greeting\">\n                Hello, {user.firstName}\n              </span>\n\n              {user.role === 'admin' && (\n                <Link to=\"/admin\" className=\"navbar-link admin-link\">\n                  <Shield size={18} />\n                  Admin Dashboard\n                </Link>\n              )}\n\n              <div className=\"user-menu\">\n                <button className=\"user-button\">\n                  <User size={18} />\n                  <span>{user.firstName}</span>\n                </button>\n                <div className=\"user-dropdown\">\n                  <Link to=\"/profile\" className=\"dropdown-item\">\n                    <User size={16} />\n                    Profile\n                  </Link>\n                  <button onClick={handleLogout} className=\"dropdown-item logout-btn\">\n                    <LogOut size={16} />\n                    Logout\n                  </button>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"navbar-auth\">\n              <Link to=\"/login\" className=\"navbar-link admin-login\">\n                <Shield size={18} />\n                Admin Login\n              </Link>\n            </div>\n          )}\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,QAAQ,cAAc;AAC1D,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBH,QAAQ,CAAC,CAAC;IACVE,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEL,OAAA;IAAKO,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACrBR,OAAA;MAAKO,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BR,OAAA,CAACP,IAAI;QAACgB,EAAE,EAAC,GAAG;QAACF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACnCR,OAAA,CAACL,KAAK;UAACY,SAAS,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChCb,OAAA;UAAAQ,QAAA,EAAM;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAEPb,OAAA;QAAKO,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBN,IAAI,gBACHF,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BR,OAAA;YAAMO,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,SACvB,EAACN,IAAI,CAACY,SAAS;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,EAENX,IAAI,CAACa,IAAI,KAAK,OAAO,iBACpBf,OAAA,CAACP,IAAI;YAACgB,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAClDR,OAAA,CAACF,MAAM;cAACkB,IAAI,EAAE;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,eAEDb,OAAA;YAAKO,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBR,OAAA;cAAQO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC7BR,OAAA,CAACJ,IAAI;gBAACoB,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClBb,OAAA;gBAAAQ,QAAA,EAAON,IAAI,CAACY;cAAS;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACTb,OAAA;cAAKO,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BR,OAAA,CAACP,IAAI;gBAACgB,EAAE,EAAC,UAAU;gBAACF,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC3CR,OAAA,CAACJ,IAAI;kBAACoB,IAAI,EAAE;gBAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAEpB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPb,OAAA;gBAAQiB,OAAO,EAAEX,YAAa;gBAACC,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACjER,OAAA,CAACH,MAAM;kBAACmB,IAAI,EAAE;gBAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAEtB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENb,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BR,OAAA,CAACP,IAAI;YAACgB,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACnDR,OAAA,CAACF,MAAM;cAACkB,IAAI,EAAE;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACT,EAAA,CA3DIH,MAAM;EAAA,QACOP,WAAW;AAAA;AAAAwB,EAAA,GADxBjB,MAAM;AA6DZ,eAAeA,MAAM;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}