import { Link, useNavigate } from 'react-router-dom';
import { Heart, User, LogOut, Shield } from 'lucide-react';
import './Navbar.css';

const Navbar = ({ user, onLogout }) => {
  const navigate = useNavigate();

  const handleLogout = () => {
    onLogout();
    navigate('/');
  };

  return (
    <nav className="navbar">
      <div className="navbar-container">
        <Link to="/" className="navbar-brand">
          <Heart className="brand-icon" />
          <span>Companion</span>
        </Link>

        <div className="navbar-menu">
          {user ? (
            <div className="navbar-user">
              <span className="user-greeting">
                Hello, {user.firstName}
              </span>

              {user.role === 'admin' && (
                <Link to="/admin" className="navbar-link admin-link">
                  <Shield size={18} />
                  Admin Dashboard
                </Link>
              )}

              <div className="user-menu">
                <button className="user-button">
                  <User size={18} />
                  <span>{user.firstName}</span>
                </button>
                <div className="user-dropdown">
                  <Link to="/profile" className="dropdown-item">
                    <User size={16} />
                    Profile
                  </Link>
                  <button onClick={handleLogout} className="dropdown-item logout-btn">
                    <LogOut size={16} />
                    Logout
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="navbar-auth">
              <Link to="/login" className="navbar-link admin-login">
                <Shield size={18} />
                Admin Login
              </Link>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
