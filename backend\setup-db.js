const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function setupDatabase() {
  let connection;
  
  try {
    console.log('🔄 Connecting to MySQL...');
    
    // Connect to MySQL without specifying database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
    });

    console.log('✅ Connected to MySQL successfully');

    // Create database if it doesn't exist
    console.log('🔄 Creating database...');
    await connection.execute(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME || 'companion'}`);
    console.log('✅ Database created/verified');

    // Close connection and reconnect to the specific database
    await connection.end();

    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'companion'
    });

    // Read and execute the schema
    console.log('🔄 Setting up tables...');
    const schemaPath = path.join(__dirname, 'database', 'schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');

    // Split the schema into individual statements and filter out USE statements
    const statements = schema.split(';').filter(stmt => {
      const trimmed = stmt.trim();
      return trimmed.length > 0 &&
             !trimmed.toUpperCase().startsWith('CREATE DATABASE') &&
             !trimmed.toUpperCase().startsWith('USE ');
    });

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement);
        } catch (error) {
          // Skip errors for statements that might already exist
          if (!error.message.includes('already exists')) {
            console.warn('Warning:', error.message);
          }
        }
      }
    }

    console.log('✅ Tables created successfully');

    // Insert default admin user (password: admin123)
    console.log('🔄 Creating default admin user...');
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('admin123', 12);
    
    await connection.execute(`
      INSERT INTO users (email, password, first_name, last_name, role) 
      VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE id=id
    `, ['<EMAIL>', hashedPassword, 'Admin', 'User', 'admin']);

    console.log('✅ Default admin user created');
    console.log('📧 Admin Email: <EMAIL>');
    console.log('🔑 Admin Password: admin123');

    console.log('\n🎉 Database setup completed successfully!');
    console.log('🚀 You can now start the server with: npm run dev');

  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Please check your database credentials in the .env file:');
      console.log('   - DB_HOST (default: localhost)');
      console.log('   - DB_USER (default: root)');
      console.log('   - DB_PASSWORD (your MySQL password)');
      console.log('   - DB_NAME (default: companion)');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the setup
setupDatabase();
