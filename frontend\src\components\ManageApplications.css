.applications-modal {
  max-width: 800px;
  max-height: 90vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 24px;
}

.modal-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
}

.request-title {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.modal-body {
  padding: 0 24px 24px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.applications-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.application-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
}

.application-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.application-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.caregiver-info {
  display: flex;
  gap: 15px;
  flex: 1;
}

.caregiver-avatar {
  width: 50px;
  height: 50px;
  background: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.caregiver-details {
  flex: 1;
}

.caregiver-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
}

.caregiver-email {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 5px 0;
}

.experience {
  color: #667eea;
  font-size: 0.85rem;
  font-weight: 500;
  margin: 0;
}

.application-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.pending {
  background: #fff3e0;
  color: #f57c00;
}

.status-badge.accepted {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-badge.rejected {
  background: #ffebee;
  color: #d32f2f;
}

.rating-section {
  margin-bottom: 15px;
}

.rating {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ffa726;
  font-size: 0.9rem;
}

.reviews-count {
  color: #666;
  margin-left: 5px;
}

.proposed-rate {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #2e7d32;
  font-weight: 500;
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.application-message {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.application-message > svg {
  color: #667eea;
  margin-bottom: 8px;
}

.message-content strong {
  color: #333;
  font-size: 0.9rem;
}

.message-content p {
  margin: 5px 0 0 0;
  color: #666;
  line-height: 1.5;
}

.caregiver-bio {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.caregiver-bio strong {
  color: #333;
  font-size: 0.9rem;
}

.caregiver-bio p {
  margin: 5px 0 0 0;
  color: #666;
  line-height: 1.5;
}

.application-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.applied-date {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 0.85rem;
}

.application-actions {
  display: flex;
  gap: 10px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
  transform: translateY(-1px);
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.empty-state svg {
  color: #ccc;
  margin-bottom: 20px;
}

.empty-state h3 {
  color: #333;
  margin-bottom: 10px;
}

.empty-state p {
  color: #666;
  margin: 0;
}

.error-state p {
  color: #d32f2f;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .applications-modal {
    max-width: 95vw;
    margin: 10px;
  }
  
  .modal-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .application-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .application-footer {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .application-actions {
    width: 100%;
  }
  
  .btn {
    flex: 1;
    justify-content: center;
  }
}
