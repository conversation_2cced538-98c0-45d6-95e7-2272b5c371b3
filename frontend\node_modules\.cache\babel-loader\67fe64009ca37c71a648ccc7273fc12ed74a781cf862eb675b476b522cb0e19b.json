{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Companion\\\\frontend\\\\src\\\\components\\\\UserDashboard.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Plus, Search, Filter, Calendar, MapPin, Clock, DollarSign, User, Heart, Star, MessageCircle, CheckCircle, XCircle } from 'lucide-react';\nimport CreateServiceRequest from './CreateServiceRequest';\nimport './UserDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserDashboard = ({\n  user\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('browse');\n  const [serviceRequests, setServiceRequests] = useState([]);\n  const [myRequests, setMyRequests] = useState([]);\n  const [myApplications, setMyApplications] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n\n  // Fetch data from API\n  useEffect(() => {\n    fetchServiceRequests();\n    if (user.role === 'user') {\n      fetchMyRequests();\n    } else if (user.role === 'caregiver') {\n      fetchMyApplications();\n    }\n  }, [user.role, selectedCategory, searchTerm]);\n  const fetchServiceRequests = async () => {\n    try {\n      setLoading(true);\n      const token = localStorage.getItem('token');\n      const params = new URLSearchParams({\n        category: selectedCategory,\n        search: searchTerm,\n        limit: 20\n      });\n      const response = await axios.get(`http://localhost:5000/api/services/requests?${params}`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setServiceRequests(response.data.data.requests);\n      }\n    } catch (error) {\n      console.error('Error fetching service requests:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchMyRequests = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/services/my-requests', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setMyRequests(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching my requests:', error);\n    }\n  };\n  const fetchMyApplications = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/services/my-applications', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setMyApplications(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching my applications:', error);\n    }\n  };\n  const handleApplyForService = async requestId => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.post(`http://localhost:5000/api/services/requests/${requestId}/apply`, {\n        message: 'I am interested in this opportunity and would love to help.'\n      }, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        alert('Application submitted successfully!');\n        fetchServiceRequests(); // Refresh the list\n        fetchMyApplications(); // Refresh applications if caregiver\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error applying for service:', error);\n      alert(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to submit application');\n    }\n  };\n  const categories = ['all', 'Baby Care', 'Elderly Care', 'Disability Support', 'Reading & Writing Help', 'Household Help', 'Medical Assistance'];\n  const filteredRequests = serviceRequests.filter(request => {\n    const matchesSearch = request.title.toLowerCase().includes(searchTerm.toLowerCase()) || request.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || request.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n  const ServiceRequestCard = ({\n    request,\n    showActions = true\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"service-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"service-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"service-title-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"service-title\",\n          children: request.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"service-category\",\n          children: request.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"service-rate\",\n        children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"$\", request.hourlyRate, \"/hr\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"service-description\",\n      children: request.description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"service-details\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item\",\n        children: [/*#__PURE__*/_jsxDEV(MapPin, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: request.location\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item\",\n        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [new Date(request.startDate).toLocaleDateString(), \" - \", new Date(request.endDate).toLocaleDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item\",\n        children: [/*#__PURE__*/_jsxDEV(User, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: request.clientName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rating\",\n          children: [/*#__PURE__*/_jsxDEV(Star, {\n            size: 14,\n            fill: \"currentColor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: request.clientRating\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"service-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"service-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"applications-count\",\n          children: [request.applicationsCount, \" applications\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `status ${request.status}`,\n          children: request.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), showActions && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"service-actions\",\n        children: user.role === 'caregiver' ? /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => handleApplyForService(request.id),\n          children: [/*#__PURE__*/_jsxDEV(Heart, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 17\n          }, this), \"Apply Now\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 17\n          }, this), \"Contact\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n  const TabButton = ({\n    id,\n    label,\n    icon,\n    isActive,\n    onClick\n  }) => /*#__PURE__*/_jsxDEV(\"button\", {\n    className: `tab-button ${isActive ? 'active' : ''}`,\n    onClick: () => onClick(id),\n    children: [icon, /*#__PURE__*/_jsxDEV(\"span\", {\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"dashboard-title\",\n          children: [\"Welcome back, \", user.firstName, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"dashboard-subtitle\",\n          children: user.role === 'caregiver' ? 'Find meaningful opportunities to help others in your community' : 'Connect with trusted caregivers for your needs'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(TabButton, {\n          id: \"browse\",\n          label: \"Browse Services\",\n          icon: /*#__PURE__*/_jsxDEV(Search, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 19\n          }, this),\n          isActive: activeTab === 'browse',\n          onClick: setActiveTab\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), user.role === 'user' && /*#__PURE__*/_jsxDEV(TabButton, {\n          id: \"my-requests\",\n          label: \"My Requests\",\n          icon: /*#__PURE__*/_jsxDEV(Calendar, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 21\n          }, this),\n          isActive: activeTab === 'my-requests',\n          onClick: setActiveTab\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this), user.role === 'caregiver' && /*#__PURE__*/_jsxDEV(TabButton, {\n          id: \"my-applications\",\n          label: \"My Applications\",\n          icon: /*#__PURE__*/_jsxDEV(Heart, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 21\n          }, this),\n          isActive: activeTab === 'my-applications',\n          onClick: setActiveTab\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n          id: \"profile\",\n          label: \"Profile\",\n          icon: /*#__PURE__*/_jsxDEV(User, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 19\n          }, this),\n          isActive: activeTab === 'profile',\n          onClick: setActiveTab\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-content\",\n        children: [activeTab === 'browse' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"browse-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"browse-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-filters\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"search-box\",\n                children: [/*#__PURE__*/_jsxDEV(Search, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search services...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedCategory,\n                onChange: e => setSelectedCategory(e.target.value),\n                className: \"category-filter\",\n                children: categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category,\n                  children: category === 'all' ? 'All Categories' : category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), user.role === 'user' && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => setShowCreateModal(true),\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this), \"Post New Request\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"services-grid\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-state\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"loading-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Loading opportunities...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 19\n            }, this) : filteredRequests.length > 0 ? filteredRequests.map(request => /*#__PURE__*/_jsxDEV(ServiceRequestCard, {\n              request: request\n            }, request.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 21\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: [/*#__PURE__*/_jsxDEV(Search, {\n                size: 48\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"No services found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Try adjusting your search criteria or check back later for new opportunities.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this), activeTab === 'my-requests' && user.role === 'user' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-requests-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"My Service Requests\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => setShowCreateModal(true),\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), \"Create New Request\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"requests-list\",\n            children: myRequests.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"services-grid\",\n              children: myRequests.map(request => /*#__PURE__*/_jsxDEV(ServiceRequestCard, {\n                request: request,\n                showActions: false\n              }, request.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 48\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"No requests yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Create your first service request to get started.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this), activeTab === 'my-applications' && user.role === 'caregiver' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-applications-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"My Applications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"applications-list\",\n            children: myApplications.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"services-grid\",\n              children: myApplications.map(application => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"service-card application-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"service-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"service-title-section\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"service-title\",\n                      children: application.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"service-category\",\n                      children: application.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"application-status\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `status ${application.status}`,\n                      children: application.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"service-description\",\n                  children: application.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"service-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detail-item\",\n                    children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: application.location\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detail-item\",\n                    children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: new Date(application.startDate).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detail-item\",\n                    children: [/*#__PURE__*/_jsxDEV(User, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 380,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: application.clientName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 27\n                  }, this), application.proposedRate && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detail-item\",\n                    children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Proposed: $\", application.proposedRate, \"/hr\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"application-footer\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"applied-date\",\n                    children: [\"Applied: \", new Date(application.appliedAt).toLocaleDateString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 25\n                }, this)]\n              }, application.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: [/*#__PURE__*/_jsxDEV(Heart, {\n                size: 48\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"No applications yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Start applying to service requests to help others in your community.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this), activeTab === 'profile' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Profile Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-avatar\",\n                children: /*#__PURE__*/_jsxDEV(User, {\n                  size: 48\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: [user.firstName, \" \", user.lastName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"role-badge\",\n                  children: user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-secondary\",\n                children: \"Edit Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreateServiceRequest, {\n      isOpen: showCreateModal,\n      onClose: () => setShowCreateModal(false),\n      onSuccess: () => {\n        fetchMyRequests();\n        fetchServiceRequests();\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this);\n};\n_s(UserDashboard, \"wlsyIDNn3dKz4/aDou0SINVYFAY=\");\n_c = UserDashboard;\nexport default UserDashboard;\nvar _c;\n$RefreshReg$(_c, \"UserDashboard\");", "map": {"version": 3, "names": ["useState", "useEffect", "axios", "Plus", "Search", "Filter", "Calendar", "MapPin", "Clock", "DollarSign", "User", "Heart", "Star", "MessageCircle", "CheckCircle", "XCircle", "CreateServiceRequest", "jsxDEV", "_jsxDEV", "UserDashboard", "user", "_s", "activeTab", "setActiveTab", "serviceRequests", "setServiceRequests", "myRequests", "setMyRequests", "myApplications", "setMyApplications", "loading", "setLoading", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "showCreateModal", "setShowCreateModal", "fetchServiceRequests", "role", "fetchMyRequests", "fetchMyApplications", "token", "localStorage", "getItem", "params", "URLSearchParams", "category", "search", "limit", "response", "get", "headers", "Authorization", "data", "success", "requests", "error", "console", "handleApplyForService", "requestId", "post", "message", "alert", "_error$response", "_error$response$data", "categories", "filteredRequests", "filter", "request", "matchesSearch", "title", "toLowerCase", "includes", "description", "matchesCategory", "ServiceRequestCard", "showActions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "hourlyRate", "location", "Date", "startDate", "toLocaleDateString", "endDate", "clientName", "fill", "clientRating", "applicationsCount", "status", "onClick", "id", "TabButton", "label", "icon", "isActive", "firstName", "type", "placeholder", "value", "onChange", "e", "target", "map", "length", "application", "proposedRate", "appliedAt", "lastName", "email", "isOpen", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Companion/frontend/src/components/UserDashboard.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Plus,\n  Search,\n  Filter,\n  Calendar,\n  MapPin,\n  Clock,\n  DollarSign,\n  User,\n  Heart,\n  Star,\n  MessageCircle,\n  CheckCircle,\n  XCircle\n} from 'lucide-react';\nimport CreateServiceRequest from './CreateServiceRequest';\nimport './UserDashboard.css';\n\nconst UserDashboard = ({ user }) => {\n  const [activeTab, setActiveTab] = useState('browse');\n  const [serviceRequests, setServiceRequests] = useState([]);\n  const [myRequests, setMyRequests] = useState([]);\n  const [myApplications, setMyApplications] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n\n  // Fetch data from API\n  useEffect(() => {\n    fetchServiceRequests();\n    if (user.role === 'user') {\n      fetchMyRequests();\n    } else if (user.role === 'caregiver') {\n      fetchMyApplications();\n    }\n  }, [user.role, selectedCategory, searchTerm]);\n\n  const fetchServiceRequests = async () => {\n    try {\n      setLoading(true);\n      const token = localStorage.getItem('token');\n      const params = new URLSearchParams({\n        category: selectedCategory,\n        search: searchTerm,\n        limit: 20\n      });\n\n      const response = await axios.get(`http://localhost:5000/api/services/requests?${params}`, {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setServiceRequests(response.data.data.requests);\n      }\n    } catch (error) {\n      console.error('Error fetching service requests:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchMyRequests = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/services/my-requests', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setMyRequests(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching my requests:', error);\n    }\n  };\n\n  const fetchMyApplications = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/services/my-applications', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setMyApplications(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching my applications:', error);\n    }\n  };\n\n  const handleApplyForService = async (requestId) => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.post(\n        `http://localhost:5000/api/services/requests/${requestId}/apply`,\n        { message: 'I am interested in this opportunity and would love to help.' },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n\n      if (response.data.success) {\n        alert('Application submitted successfully!');\n        fetchServiceRequests(); // Refresh the list\n        fetchMyApplications(); // Refresh applications if caregiver\n      }\n    } catch (error) {\n      console.error('Error applying for service:', error);\n      alert(error.response?.data?.message || 'Failed to submit application');\n    }\n  };\n\n  const categories = [\n    'all',\n    'Baby Care',\n    'Elderly Care',\n    'Disability Support',\n    'Reading & Writing Help',\n    'Household Help',\n    'Medical Assistance'\n  ];\n\n  const filteredRequests = serviceRequests.filter(request => {\n    const matchesSearch = request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         request.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || request.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const ServiceRequestCard = ({ request, showActions = true }) => (\n    <div className=\"service-card\">\n      <div className=\"service-header\">\n        <div className=\"service-title-section\">\n          <h3 className=\"service-title\">{request.title}</h3>\n          <span className=\"service-category\">{request.category}</span>\n        </div>\n        <div className=\"service-rate\">\n          <DollarSign size={16} />\n          <span>${request.hourlyRate}/hr</span>\n        </div>\n      </div>\n      \n      <p className=\"service-description\">{request.description}</p>\n      \n      <div className=\"service-details\">\n        <div className=\"detail-item\">\n          <MapPin size={16} />\n          <span>{request.location}</span>\n        </div>\n        <div className=\"detail-item\">\n          <Calendar size={16} />\n          <span>{new Date(request.startDate).toLocaleDateString()} - {new Date(request.endDate).toLocaleDateString()}</span>\n        </div>\n        <div className=\"detail-item\">\n          <User size={16} />\n          <span>{request.clientName}</span>\n          <div className=\"rating\">\n            <Star size={14} fill=\"currentColor\" />\n            <span>{request.clientRating}</span>\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"service-footer\">\n        <div className=\"service-stats\">\n          <span className=\"applications-count\">{request.applicationsCount} applications</span>\n          <span className={`status ${request.status}`}>{request.status}</span>\n        </div>\n        \n        {showActions && (\n          <div className=\"service-actions\">\n            {user.role === 'caregiver' ? (\n              <button\n                className=\"btn btn-primary\"\n                onClick={() => handleApplyForService(request.id)}\n              >\n                <Heart size={16} />\n                Apply Now\n              </button>\n            ) : (\n              <button className=\"btn btn-secondary\">\n                <MessageCircle size={16} />\n                Contact\n              </button>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n\n  const TabButton = ({ id, label, icon, isActive, onClick }) => (\n    <button\n      className={`tab-button ${isActive ? 'active' : ''}`}\n      onClick={() => onClick(id)}\n    >\n      {icon}\n      <span>{label}</span>\n    </button>\n  );\n\n  return (\n    <div className=\"user-dashboard\">\n      <div className=\"dashboard-container\">\n        <div className=\"dashboard-header\">\n          <h1 className=\"dashboard-title\">\n            Welcome back, {user.firstName}!\n          </h1>\n          <p className=\"dashboard-subtitle\">\n            {user.role === 'caregiver' \n              ? 'Find meaningful opportunities to help others in your community'\n              : 'Connect with trusted caregivers for your needs'\n            }\n          </p>\n        </div>\n\n        <div className=\"dashboard-tabs\">\n          <TabButton\n            id=\"browse\"\n            label=\"Browse Services\"\n            icon={<Search size={20} />}\n            isActive={activeTab === 'browse'}\n            onClick={setActiveTab}\n          />\n          {user.role === 'user' && (\n            <TabButton\n              id=\"my-requests\"\n              label=\"My Requests\"\n              icon={<Calendar size={20} />}\n              isActive={activeTab === 'my-requests'}\n              onClick={setActiveTab}\n            />\n          )}\n          {user.role === 'caregiver' && (\n            <TabButton\n              id=\"my-applications\"\n              label=\"My Applications\"\n              icon={<Heart size={20} />}\n              isActive={activeTab === 'my-applications'}\n              onClick={setActiveTab}\n            />\n          )}\n          <TabButton\n            id=\"profile\"\n            label=\"Profile\"\n            icon={<User size={20} />}\n            isActive={activeTab === 'profile'}\n            onClick={setActiveTab}\n          />\n        </div>\n\n        <div className=\"dashboard-content\">\n          {activeTab === 'browse' && (\n            <div className=\"browse-section\">\n              <div className=\"browse-header\">\n                <div className=\"search-filters\">\n                  <div className=\"search-box\">\n                    <Search size={20} />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search services...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                    />\n                  </div>\n                  \n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className=\"category-filter\"\n                  >\n                    {categories.map(category => (\n                      <option key={category} value={category}>\n                        {category === 'all' ? 'All Categories' : category}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n                \n                {user.role === 'user' && (\n                  <button\n                    className=\"btn btn-primary\"\n                    onClick={() => setShowCreateModal(true)}\n                  >\n                    <Plus size={20} />\n                    Post New Request\n                  </button>\n                )}\n              </div>\n\n              <div className=\"services-grid\">\n                {loading ? (\n                  <div className=\"loading-state\">\n                    <div className=\"loading-spinner\"></div>\n                    <p>Loading opportunities...</p>\n                  </div>\n                ) : filteredRequests.length > 0 ? (\n                  filteredRequests.map(request => (\n                    <ServiceRequestCard key={request.id} request={request} />\n                  ))\n                ) : (\n                  <div className=\"empty-state\">\n                    <Search size={48} />\n                    <h3>No services found</h3>\n                    <p>Try adjusting your search criteria or check back later for new opportunities.</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'my-requests' && user.role === 'user' && (\n            <div className=\"my-requests-section\">\n              <div className=\"section-header\">\n                <h2>My Service Requests</h2>\n                <button\n                  className=\"btn btn-primary\"\n                  onClick={() => setShowCreateModal(true)}\n                >\n                  <Plus size={20} />\n                  Create New Request\n                </button>\n              </div>\n\n              <div className=\"requests-list\">\n                {myRequests.length > 0 ? (\n                  <div className=\"services-grid\">\n                    {myRequests.map(request => (\n                      <ServiceRequestCard key={request.id} request={request} showActions={false} />\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"empty-state\">\n                    <Calendar size={48} />\n                    <h3>No requests yet</h3>\n                    <p>Create your first service request to get started.</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'my-applications' && user.role === 'caregiver' && (\n            <div className=\"my-applications-section\">\n              <div className=\"section-header\">\n                <h2>My Applications</h2>\n              </div>\n\n              <div className=\"applications-list\">\n                {myApplications.length > 0 ? (\n                  <div className=\"services-grid\">\n                    {myApplications.map(application => (\n                      <div key={application.id} className=\"service-card application-card\">\n                        <div className=\"service-header\">\n                          <div className=\"service-title-section\">\n                            <h3 className=\"service-title\">{application.title}</h3>\n                            <span className=\"service-category\">{application.category}</span>\n                          </div>\n                          <div className=\"application-status\">\n                            <span className={`status ${application.status}`}>\n                              {application.status}\n                            </span>\n                          </div>\n                        </div>\n\n                        <p className=\"service-description\">{application.description}</p>\n\n                        <div className=\"service-details\">\n                          <div className=\"detail-item\">\n                            <MapPin size={16} />\n                            <span>{application.location}</span>\n                          </div>\n                          <div className=\"detail-item\">\n                            <Calendar size={16} />\n                            <span>{new Date(application.startDate).toLocaleDateString()}</span>\n                          </div>\n                          <div className=\"detail-item\">\n                            <User size={16} />\n                            <span>{application.clientName}</span>\n                          </div>\n                          {application.proposedRate && (\n                            <div className=\"detail-item\">\n                              <DollarSign size={16} />\n                              <span>Proposed: ${application.proposedRate}/hr</span>\n                            </div>\n                          )}\n                        </div>\n\n                        <div className=\"application-footer\">\n                          <small className=\"applied-date\">\n                            Applied: {new Date(application.appliedAt).toLocaleDateString()}\n                          </small>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"empty-state\">\n                    <Heart size={48} />\n                    <h3>No applications yet</h3>\n                    <p>Start applying to service requests to help others in your community.</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'profile' && (\n            <div className=\"profile-section\">\n              <div className=\"section-header\">\n                <h2>Profile Settings</h2>\n              </div>\n              \n              <div className=\"profile-content\">\n                <div className=\"profile-card\">\n                  <div className=\"profile-avatar\">\n                    <User size={48} />\n                  </div>\n                  <div className=\"profile-info\">\n                    <h3>{user.firstName} {user.lastName}</h3>\n                    <p>{user.email}</p>\n                    <span className=\"role-badge\">{user.role}</span>\n                  </div>\n                  <button className=\"btn btn-secondary\">Edit Profile</button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      <CreateServiceRequest\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        onSuccess={() => {\n          fetchMyRequests();\n          fetchServiceRequests();\n        }}\n      />\n    </div>\n  );\n};\n\nexport default UserDashboard;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,aAAa,EACbC,WAAW,EACXC,OAAO,QACF,cAAc;AACrB,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACdqC,oBAAoB,CAAC,CAAC;IACtB,IAAIlB,IAAI,CAACmB,IAAI,KAAK,MAAM,EAAE;MACxBC,eAAe,CAAC,CAAC;IACnB,CAAC,MAAM,IAAIpB,IAAI,CAACmB,IAAI,KAAK,WAAW,EAAE;MACpCE,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACrB,IAAI,CAACmB,IAAI,EAAEL,gBAAgB,EAAEF,UAAU,CAAC,CAAC;EAE7C,MAAMM,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMW,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCC,QAAQ,EAAEb,gBAAgB;QAC1Bc,MAAM,EAAEhB,UAAU;QAClBiB,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,MAAMC,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,+CAA+CN,MAAM,EAAE,EAAE;QACxFO,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUX,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIQ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzB9B,kBAAkB,CAACyB,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACE,QAAQ,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMM,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,gDAAgD,EAAE;QACjFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUX,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIQ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzB5B,aAAa,CAACuB,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;MACnC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMhB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMM,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,oDAAoD,EAAE;QACrFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUX,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIQ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzB1B,iBAAiB,CAACqB,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;MACvC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAME,qBAAqB,GAAG,MAAOC,SAAS,IAAK;IACjD,IAAI;MACF,MAAMlB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMM,QAAQ,GAAG,MAAMhD,KAAK,CAAC2D,IAAI,CAC/B,+CAA+CD,SAAS,QAAQ,EAChE;QAAEE,OAAO,EAAE;MAA8D,CAAC,EAC1E;QAAEV,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUX,KAAK;QAAG;MAAE,CAClD,CAAC;MAED,IAAIQ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBQ,KAAK,CAAC,qCAAqC,CAAC;QAC5CzB,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACxBG,mBAAmB,CAAC,CAAC,CAAC,CAAC;MACzB;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MAAA,IAAAO,eAAA,EAAAC,oBAAA;MACdP,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDM,KAAK,CAAC,EAAAC,eAAA,GAAAP,KAAK,CAACP,QAAQ,cAAAc,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBH,OAAO,KAAI,8BAA8B,CAAC;IACxE;EACF,CAAC;EAED,MAAMI,UAAU,GAAG,CACjB,KAAK,EACL,WAAW,EACX,cAAc,EACd,oBAAoB,EACpB,wBAAwB,EACxB,gBAAgB,EAChB,oBAAoB,CACrB;EAED,MAAMC,gBAAgB,GAAG3C,eAAe,CAAC4C,MAAM,CAACC,OAAO,IAAI;IACzD,MAAMC,aAAa,GAAGD,OAAO,CAACE,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,UAAU,CAACwC,WAAW,CAAC,CAAC,CAAC,IAC/DH,OAAO,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,UAAU,CAACwC,WAAW,CAAC,CAAC,CAAC;IACzF,MAAMG,eAAe,GAAGzC,gBAAgB,KAAK,KAAK,IAAImC,OAAO,CAACtB,QAAQ,KAAKb,gBAAgB;IAC3F,OAAOoC,aAAa,IAAIK,eAAe;EACzC,CAAC,CAAC;EAEF,MAAMC,kBAAkB,GAAGA,CAAC;IAAEP,OAAO;IAAEQ,WAAW,GAAG;EAAK,CAAC,kBACzD3D,OAAA;IAAK4D,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B7D,OAAA;MAAK4D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B7D,OAAA;QAAK4D,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC7D,OAAA;UAAI4D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEV,OAAO,CAACE;QAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClDjE,OAAA;UAAM4D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAEV,OAAO,CAACtB;QAAQ;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACNjE,OAAA;QAAK4D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7D,OAAA,CAACT,UAAU;UAAC2E,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxBjE,OAAA;UAAA6D,QAAA,GAAM,GAAC,EAACV,OAAO,CAACgB,UAAU,EAAC,KAAG;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjE,OAAA;MAAG4D,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EAAEV,OAAO,CAACK;IAAW;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE5DjE,OAAA;MAAK4D,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B7D,OAAA;QAAK4D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7D,OAAA,CAACX,MAAM;UAAC6E,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpBjE,OAAA;UAAA6D,QAAA,EAAOV,OAAO,CAACiB;QAAQ;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACNjE,OAAA;QAAK4D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7D,OAAA,CAACZ,QAAQ;UAAC8E,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtBjE,OAAA;UAAA6D,QAAA,GAAO,IAAIQ,IAAI,CAAClB,OAAO,CAACmB,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAC,KAAG,EAAC,IAAIF,IAAI,CAAClB,OAAO,CAACqB,OAAO,CAAC,CAACD,kBAAkB,CAAC,CAAC;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/G,CAAC,eACNjE,OAAA;QAAK4D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7D,OAAA,CAACR,IAAI;UAAC0E,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClBjE,OAAA;UAAA6D,QAAA,EAAOV,OAAO,CAACsB;QAAU;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjCjE,OAAA;UAAK4D,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrB7D,OAAA,CAACN,IAAI;YAACwE,IAAI,EAAE,EAAG;YAACQ,IAAI,EAAC;UAAc;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtCjE,OAAA;YAAA6D,QAAA,EAAOV,OAAO,CAACwB;UAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjE,OAAA;MAAK4D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B7D,OAAA;QAAK4D,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B7D,OAAA;UAAM4D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAEV,OAAO,CAACyB,iBAAiB,EAAC,eAAa;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFjE,OAAA;UAAM4D,SAAS,EAAE,UAAUT,OAAO,CAAC0B,MAAM,EAAG;UAAAhB,QAAA,EAAEV,OAAO,CAAC0B;QAAM;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,EAELN,WAAW,iBACV3D,OAAA;QAAK4D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7B3D,IAAI,CAACmB,IAAI,KAAK,WAAW,gBACxBrB,OAAA;UACE4D,SAAS,EAAC,iBAAiB;UAC3BkB,OAAO,EAAEA,CAAA,KAAMrC,qBAAqB,CAACU,OAAO,CAAC4B,EAAE,CAAE;UAAAlB,QAAA,gBAEjD7D,OAAA,CAACP,KAAK;YAACyE,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAErB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETjE,OAAA;UAAQ4D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACnC7D,OAAA,CAACL,aAAa;YAACuE,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAE7B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMe,SAAS,GAAGA,CAAC;IAAED,EAAE;IAAEE,KAAK;IAAEC,IAAI;IAAEC,QAAQ;IAAEL;EAAQ,CAAC,kBACvD9E,OAAA;IACE4D,SAAS,EAAE,cAAcuB,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;IACpDL,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAACC,EAAE,CAAE;IAAAlB,QAAA,GAE1BqB,IAAI,eACLlF,OAAA;MAAA6D,QAAA,EAAOoB;IAAK;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CACT;EAED,oBACEjE,OAAA;IAAK4D,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B7D,OAAA;MAAK4D,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC7D,OAAA;QAAK4D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B7D,OAAA;UAAI4D,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,gBAChB,EAAC3D,IAAI,CAACkF,SAAS,EAAC,GAChC;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjE,OAAA;UAAG4D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC9B3D,IAAI,CAACmB,IAAI,KAAK,WAAW,GACtB,gEAAgE,GAChE;QAAgD;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENjE,OAAA;QAAK4D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7D,OAAA,CAACgF,SAAS;UACRD,EAAE,EAAC,QAAQ;UACXE,KAAK,EAAC,iBAAiB;UACvBC,IAAI,eAAElF,OAAA,CAACd,MAAM;YAACgF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BkB,QAAQ,EAAE/E,SAAS,KAAK,QAAS;UACjC0E,OAAO,EAAEzE;QAAa;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,EACD/D,IAAI,CAACmB,IAAI,KAAK,MAAM,iBACnBrB,OAAA,CAACgF,SAAS;UACRD,EAAE,EAAC,aAAa;UAChBE,KAAK,EAAC,aAAa;UACnBC,IAAI,eAAElF,OAAA,CAACZ,QAAQ;YAAC8E,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BkB,QAAQ,EAAE/E,SAAS,KAAK,aAAc;UACtC0E,OAAO,EAAEzE;QAAa;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACF,EACA/D,IAAI,CAACmB,IAAI,KAAK,WAAW,iBACxBrB,OAAA,CAACgF,SAAS;UACRD,EAAE,EAAC,iBAAiB;UACpBE,KAAK,EAAC,iBAAiB;UACvBC,IAAI,eAAElF,OAAA,CAACP,KAAK;YAACyE,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BkB,QAAQ,EAAE/E,SAAS,KAAK,iBAAkB;UAC1C0E,OAAO,EAAEzE;QAAa;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACF,eACDjE,OAAA,CAACgF,SAAS;UACRD,EAAE,EAAC,SAAS;UACZE,KAAK,EAAC,SAAS;UACfC,IAAI,eAAElF,OAAA,CAACR,IAAI;YAAC0E,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBkB,QAAQ,EAAE/E,SAAS,KAAK,SAAU;UAClC0E,OAAO,EAAEzE;QAAa;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENjE,OAAA;QAAK4D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAC/BzD,SAAS,KAAK,QAAQ,iBACrBJ,OAAA;UAAK4D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B7D,OAAA;YAAK4D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7D,OAAA;cAAK4D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7D,OAAA;gBAAK4D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7D,OAAA,CAACd,MAAM;kBAACgF,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpBjE,OAAA;kBACEqF,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,oBAAoB;kBAChCC,KAAK,EAAEzE,UAAW;kBAClB0E,QAAQ,EAAGC,CAAC,IAAK1E,aAAa,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENjE,OAAA;gBACEuF,KAAK,EAAEvE,gBAAiB;gBACxBwE,QAAQ,EAAGC,CAAC,IAAKxE,mBAAmB,CAACwE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACrD3B,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAE1Bb,UAAU,CAAC2C,GAAG,CAAC9D,QAAQ,iBACtB7B,OAAA;kBAAuBuF,KAAK,EAAE1D,QAAS;kBAAAgC,QAAA,EACpChC,QAAQ,KAAK,KAAK,GAAG,gBAAgB,GAAGA;gBAAQ,GADtCA,QAAQ;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAEL/D,IAAI,CAACmB,IAAI,KAAK,MAAM,iBACnBrB,OAAA;cACE4D,SAAS,EAAC,iBAAiB;cAC3BkB,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC,IAAI,CAAE;cAAA0C,QAAA,gBAExC7D,OAAA,CAACf,IAAI;gBAACiF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENjE,OAAA;YAAK4D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BjD,OAAO,gBACNZ,OAAA;cAAK4D,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B7D,OAAA;gBAAK4D,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvCjE,OAAA;gBAAA6D,QAAA,EAAG;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,GACJhB,gBAAgB,CAAC2C,MAAM,GAAG,CAAC,GAC7B3C,gBAAgB,CAAC0C,GAAG,CAACxC,OAAO,iBAC1BnD,OAAA,CAAC0D,kBAAkB;cAAkBP,OAAO,EAAEA;YAAQ,GAA7BA,OAAO,CAAC4B,EAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqB,CACzD,CAAC,gBAEFjE,OAAA;cAAK4D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7D,OAAA,CAACd,MAAM;gBAACgF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBjE,OAAA;gBAAA6D,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BjE,OAAA;gBAAA6D,QAAA,EAAG;cAA6E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA7D,SAAS,KAAK,aAAa,IAAIF,IAAI,CAACmB,IAAI,KAAK,MAAM,iBAClDrB,OAAA;UAAK4D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC7D,OAAA;YAAK4D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B7D,OAAA;cAAA6D,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BjE,OAAA;cACE4D,SAAS,EAAC,iBAAiB;cAC3BkB,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC,IAAI,CAAE;cAAA0C,QAAA,gBAExC7D,OAAA,CAACf,IAAI;gBAACiF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENjE,OAAA;YAAK4D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BrD,UAAU,CAACoF,MAAM,GAAG,CAAC,gBACpB5F,OAAA;cAAK4D,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3BrD,UAAU,CAACmF,GAAG,CAACxC,OAAO,iBACrBnD,OAAA,CAAC0D,kBAAkB;gBAAkBP,OAAO,EAAEA,OAAQ;gBAACQ,WAAW,EAAE;cAAM,GAAjDR,OAAO,CAAC4B,EAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAyC,CAC7E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAENjE,OAAA;cAAK4D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7D,OAAA,CAACZ,QAAQ;gBAAC8E,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtBjE,OAAA;gBAAA6D,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBjE,OAAA;gBAAA6D,QAAA,EAAG;cAAiD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA7D,SAAS,KAAK,iBAAiB,IAAIF,IAAI,CAACmB,IAAI,KAAK,WAAW,iBAC3DrB,OAAA;UAAK4D,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC7D,OAAA;YAAK4D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B7D,OAAA;cAAA6D,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAENjE,OAAA;YAAK4D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC/BnD,cAAc,CAACkF,MAAM,GAAG,CAAC,gBACxB5F,OAAA;cAAK4D,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3BnD,cAAc,CAACiF,GAAG,CAACE,WAAW,iBAC7B7F,OAAA;gBAA0B4D,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBACjE7D,OAAA;kBAAK4D,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B7D,OAAA;oBAAK4D,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpC7D,OAAA;sBAAI4D,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEgC,WAAW,CAACxC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtDjE,OAAA;sBAAM4D,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEgC,WAAW,CAAChE;oBAAQ;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNjE,OAAA;oBAAK4D,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,eACjC7D,OAAA;sBAAM4D,SAAS,EAAE,UAAUiC,WAAW,CAAChB,MAAM,EAAG;sBAAAhB,QAAA,EAC7CgC,WAAW,CAAChB;oBAAM;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENjE,OAAA;kBAAG4D,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAEgC,WAAW,CAACrC;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAEhEjE,OAAA;kBAAK4D,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9B7D,OAAA;oBAAK4D,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B7D,OAAA,CAACX,MAAM;sBAAC6E,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACpBjE,OAAA;sBAAA6D,QAAA,EAAOgC,WAAW,CAACzB;oBAAQ;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACNjE,OAAA;oBAAK4D,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B7D,OAAA,CAACZ,QAAQ;sBAAC8E,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtBjE,OAAA;sBAAA6D,QAAA,EAAO,IAAIQ,IAAI,CAACwB,WAAW,CAACvB,SAAS,CAAC,CAACC,kBAAkB,CAAC;oBAAC;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACNjE,OAAA;oBAAK4D,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B7D,OAAA,CAACR,IAAI;sBAAC0E,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClBjE,OAAA;sBAAA6D,QAAA,EAAOgC,WAAW,CAACpB;oBAAU;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,EACL4B,WAAW,CAACC,YAAY,iBACvB9F,OAAA;oBAAK4D,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B7D,OAAA,CAACT,UAAU;sBAAC2E,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACxBjE,OAAA;sBAAA6D,QAAA,GAAM,aAAW,EAACgC,WAAW,CAACC,YAAY,EAAC,KAAG;oBAAA;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENjE,OAAA;kBAAK4D,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,eACjC7D,OAAA;oBAAO4D,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAAC,WACrB,EAAC,IAAIQ,IAAI,CAACwB,WAAW,CAACE,SAAS,CAAC,CAACxB,kBAAkB,CAAC,CAAC;kBAAA;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAxCE4B,WAAW,CAACd,EAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyCnB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAENjE,OAAA;cAAK4D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7D,OAAA,CAACP,KAAK;gBAACyE,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnBjE,OAAA;gBAAA6D,QAAA,EAAI;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BjE,OAAA;gBAAA6D,QAAA,EAAG;cAAoE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA7D,SAAS,KAAK,SAAS,iBACtBJ,OAAA;UAAK4D,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B7D,OAAA;YAAK4D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B7D,OAAA;cAAA6D,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAENjE,OAAA;YAAK4D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B7D,OAAA;cAAK4D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B7D,OAAA;gBAAK4D,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7B7D,OAAA,CAACR,IAAI;kBAAC0E,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACNjE,OAAA;gBAAK4D,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7D,OAAA;kBAAA6D,QAAA,GAAK3D,IAAI,CAACkF,SAAS,EAAC,GAAC,EAAClF,IAAI,CAAC8F,QAAQ;gBAAA;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzCjE,OAAA;kBAAA6D,QAAA,EAAI3D,IAAI,CAAC+F;gBAAK;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnBjE,OAAA;kBAAM4D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE3D,IAAI,CAACmB;gBAAI;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNjE,OAAA;gBAAQ4D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjE,OAAA,CAACF,oBAAoB;MACnBoG,MAAM,EAAEhF,eAAgB;MACxBiF,OAAO,EAAEA,CAAA,KAAMhF,kBAAkB,CAAC,KAAK,CAAE;MACzCiF,SAAS,EAAEA,CAAA,KAAM;QACf9E,eAAe,CAAC,CAAC;QACjBF,oBAAoB,CAAC,CAAC;MACxB;IAAE;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAvaIF,aAAa;AAAAoG,EAAA,GAAbpG,aAAa;AAyanB,eAAeA,aAAa;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}