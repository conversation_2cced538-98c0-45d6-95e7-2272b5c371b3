# Companion - Care Marketplace Web Application

A marketplace for everyday help and care, connecting people who need assistance with compassionate caregivers in their community.

## Features

- **User Authentication**: Secure login and registration system
- **Role-based Access**: Support for users, caregivers, and administrators
- **Landing Page**: Beautiful landing page with service categories and inspirational quotes
- **Admin Dashboard**: Administrative interface for managing users and services
- **Responsive Design**: Mobile-friendly interface
- **Modern UI**: Clean, professional design with smooth animations

## Tech Stack

### Frontend
- **React** - User interface library
- **React Router** - Client-side routing
- **Axios** - HTTP client for API requests
- **Lucide React** - Beautiful icons
- **CSS3** - Modern styling with animations

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web application framework
- **MySQL** - Relational database
- **JWT** - JSON Web Tokens for authentication
- **bcryptjs** - Password hashing
- **CORS** - Cross-origin resource sharing

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- MySQL (v8.0 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd companion
   ```

2. **Set up the database**
   - Create a MySQL database named `companion`
   - Run the SQL script in `setup-database.sql` to create tables and insert initial data
   - Or manually run the schema from `backend/database/schema.sql`

3. **Configure the backend**
   ```bash
   cd backend
   npm install
   ```
   
   - Update the `.env` file with your database credentials:
   ```env
   PORT=5000
   DB_HOST=localhost
   DB_USER=your_mysql_username
   DB_PASSWORD=your_mysql_password
   DB_NAME=companion
   JWT_SECRET=your_jwt_secret_key_here
   JWT_EXPIRES_IN=7d
   NODE_ENV=development
   ```

4. **Configure the frontend**
   ```bash
   cd ../frontend
   npm install
   ```

### Running the Application

1. **Start the backend server**
   ```bash
   cd backend
   npm run dev
   ```
   The backend will run on http://localhost:5000

2. **Start the frontend development server**
   ```bash
   cd frontend
   npm start
   ```
   The frontend will run on http://localhost:3000

3. **Access the application**
   - Open your browser and navigate to http://localhost:3000
   - The landing page will be displayed with navigation options

## Default Admin Account

A default admin account is created during database setup:
- **Email**: <EMAIL>
- **Password**: admin123

Use this account to access the admin dashboard at `/admin`.

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/profile` - Get current user profile

### Health Check
- `GET /api/health` - API health status

## Project Structure

```
companion/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # React components
│   │   │   ├── Navbar.js    # Navigation component
│   │   │   ├── LandingPage.js # Landing page
│   │   │   ├── Login.js     # Login form
│   │   │   ├── Signup.js    # Registration form
│   │   │   └── AdminDashboard.js # Admin interface
│   │   ├── App.js           # Main application component
│   │   └── App.css          # Global styles
│   └── package.json
├── backend/                  # Node.js backend application
│   ├── config/
│   │   └── database.js      # Database configuration
│   ├── middleware/
│   │   └── auth.js          # Authentication middleware
│   ├── routes/
│   │   └── auth.js          # Authentication routes
│   ├── database/
│   │   └── schema.sql       # Database schema
│   ├── server.js            # Main server file
│   ├── .env                 # Environment variables
│   └── package.json
├── setup-database.sql       # Database setup script
└── README.md               # This file
```

## Service Categories

The application supports the following care services:

1. **Baby Care** - Professional baby sitting and child care services
2. **Elderly Care** - Compassionate care for elderly individuals
3. **Disability Support** - Assistance for individuals with disabilities
4. **Reading & Writing Help** - Support for literacy and educational needs
5. **Household Help** - General household assistance and maintenance
6. **Medical Assistance** - Basic medical support and companionship

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the ISC License.

## Support

For support or questions, please contact the development team.
