const mysql = require('mysql2/promise');
require('dotenv').config();

async function addBlockedColumn() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'companion'
    });

    console.log('Adding is_blocked column to users table...');
    
    // Check if column already exists
    const [columns] = await connection.execute(
      "SHOW COLUMNS FROM users LIKE 'is_blocked'"
    );

    if (columns.length === 0) {
      await connection.execute(
        'ALTER TABLE users ADD COLUMN is_blocked BOOLEAN DEFAULT FALSE AFTER is_verified'
      );
      console.log('✅ is_blocked column added successfully');
    } else {
      console.log('✅ is_blocked column already exists');
    }

  } catch (error) {
    console.error('❌ Error adding column:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

addBlockedColumn();
