{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Companion\\\\frontend\\\\src\\\\components\\\\UserDashboard.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Plus, Search, Filter, Calendar, MapPin, Clock, DollarSign, User, Heart, Star, MessageCircle, CheckCircle, XCircle } from 'lucide-react';\nimport CreateServiceRequest from './CreateServiceRequest';\nimport ManageApplications from './ManageApplications';\nimport './UserDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserDashboard = ({\n  user\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('browse');\n  const [serviceRequests, setServiceRequests] = useState([]);\n  const [myRequests, setMyRequests] = useState([]);\n  const [myApplications, setMyApplications] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showApplicationsModal, setShowApplicationsModal] = useState(false);\n  const [selectedRequest, setSelectedRequest] = useState(null);\n\n  // Fetch data from API\n  useEffect(() => {\n    fetchServiceRequests();\n    if (user.role === 'user') {\n      fetchMyRequests();\n    } else if (user.role === 'caregiver') {\n      fetchMyApplications();\n    }\n  }, [user.role, selectedCategory, searchTerm]);\n  const fetchServiceRequests = async () => {\n    try {\n      setLoading(true);\n      const token = localStorage.getItem('token');\n      const params = new URLSearchParams({\n        category: selectedCategory,\n        search: searchTerm,\n        limit: 20\n      });\n      const response = await axios.get(`http://localhost:5000/api/services/requests?${params}`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setServiceRequests(response.data.data.requests);\n      }\n    } catch (error) {\n      console.error('Error fetching service requests:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchMyRequests = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/services/my-requests', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setMyRequests(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching my requests:', error);\n    }\n  };\n  const fetchMyApplications = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/services/my-applications', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setMyApplications(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching my applications:', error);\n    }\n  };\n  const handleApplyForService = async requestId => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.post(`http://localhost:5000/api/services/requests/${requestId}/apply`, {\n        message: 'I am interested in this opportunity and would love to help.'\n      }, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        alert('Application submitted successfully!');\n        fetchServiceRequests(); // Refresh the list\n        fetchMyApplications(); // Refresh applications if caregiver\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error applying for service:', error);\n      alert(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to submit application');\n    }\n  };\n  const handleViewApplications = request => {\n    setSelectedRequest(request);\n    setShowApplicationsModal(true);\n  };\n  const categories = ['all', 'Baby Care', 'Elderly Care', 'Disability Support', 'Reading & Writing Help', 'Household Help', 'Medical Assistance'];\n  const filteredRequests = serviceRequests.filter(request => {\n    const matchesSearch = request.title.toLowerCase().includes(searchTerm.toLowerCase()) || request.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || request.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n  const ServiceRequestCard = ({\n    request,\n    showActions = true\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"service-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"service-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"service-title-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"service-title\",\n          children: request.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"service-category\",\n          children: request.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"service-rate\",\n        children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"$\", request.hourlyRate, \"/hr\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"service-description\",\n      children: request.description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"service-details\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item\",\n        children: [/*#__PURE__*/_jsxDEV(MapPin, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: request.location\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item\",\n        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [new Date(request.startDate).toLocaleDateString(), \" - \", new Date(request.endDate).toLocaleDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item\",\n        children: [/*#__PURE__*/_jsxDEV(User, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: request.clientName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rating\",\n          children: [/*#__PURE__*/_jsxDEV(Star, {\n            size: 14,\n            fill: \"currentColor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: request.clientRating\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"service-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"service-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"applications-count\",\n          children: [request.applicationsCount, \" applications\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `status ${request.status}`,\n          children: request.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), showActions && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"service-actions\",\n        children: user.role === 'caregiver' ? /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => handleApplyForService(request.id),\n          children: [/*#__PURE__*/_jsxDEV(Heart, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 17\n          }, this), \"Apply Now\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 15\n        }, this) : user.role === 'user' && request.clientName && request.clientName.includes(user.firstName) ? /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: () => handleViewApplications(request),\n          children: [/*#__PURE__*/_jsxDEV(User, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 17\n          }, this), \"View Applications (\", request.applicationsCount, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 17\n          }, this), \"Contact\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n  const TabButton = ({\n    id,\n    label,\n    icon,\n    isActive,\n    onClick\n  }) => /*#__PURE__*/_jsxDEV(\"button\", {\n    className: `tab-button ${isActive ? 'active' : ''}`,\n    onClick: () => onClick(id),\n    children: [icon, /*#__PURE__*/_jsxDEV(\"span\", {\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"dashboard-title\",\n          children: [\"Welcome back, \", user.firstName, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"dashboard-subtitle\",\n          children: user.role === 'caregiver' ? 'Find meaningful opportunities to help others in your community' : 'Connect with trusted caregivers for your needs'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(TabButton, {\n          id: \"browse\",\n          label: \"Browse Services\",\n          icon: /*#__PURE__*/_jsxDEV(Search, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 19\n          }, this),\n          isActive: activeTab === 'browse',\n          onClick: setActiveTab\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), user.role === 'user' && /*#__PURE__*/_jsxDEV(TabButton, {\n          id: \"my-requests\",\n          label: \"My Requests\",\n          icon: /*#__PURE__*/_jsxDEV(Calendar, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 21\n          }, this),\n          isActive: activeTab === 'my-requests',\n          onClick: setActiveTab\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), user.role === 'caregiver' && /*#__PURE__*/_jsxDEV(TabButton, {\n          id: \"my-applications\",\n          label: \"My Applications\",\n          icon: /*#__PURE__*/_jsxDEV(Heart, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 21\n          }, this),\n          isActive: activeTab === 'my-applications',\n          onClick: setActiveTab\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n          id: \"profile\",\n          label: \"Profile\",\n          icon: /*#__PURE__*/_jsxDEV(User, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 19\n          }, this),\n          isActive: activeTab === 'profile',\n          onClick: setActiveTab\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-content\",\n        children: [activeTab === 'browse' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"browse-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"browse-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-filters\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"search-box\",\n                children: [/*#__PURE__*/_jsxDEV(Search, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search services...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedCategory,\n                onChange: e => setSelectedCategory(e.target.value),\n                className: \"category-filter\",\n                children: categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category,\n                  children: category === 'all' ? 'All Categories' : category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), user.role === 'user' && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => setShowCreateModal(true),\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 21\n              }, this), \"Post New Request\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"services-grid\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-state\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"loading-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Loading opportunities...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 19\n            }, this) : filteredRequests.length > 0 ? filteredRequests.map(request => /*#__PURE__*/_jsxDEV(ServiceRequestCard, {\n              request: request\n            }, request.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 21\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: [/*#__PURE__*/_jsxDEV(Search, {\n                size: 48\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"No services found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Try adjusting your search criteria or check back later for new opportunities.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), activeTab === 'my-requests' && user.role === 'user' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-requests-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"My Service Requests\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => setShowCreateModal(true),\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this), \"Create New Request\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"requests-list\",\n            children: myRequests.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"services-grid\",\n              children: myRequests.map(request => /*#__PURE__*/_jsxDEV(ServiceRequestCard, {\n                request: request,\n                showActions: false\n              }, request.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 48\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"No requests yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Create your first service request to get started.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 13\n        }, this), activeTab === 'my-applications' && user.role === 'caregiver' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-applications-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"My Applications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"applications-list\",\n            children: myApplications.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"services-grid\",\n              children: myApplications.map(application => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"service-card application-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"service-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"service-title-section\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"service-title\",\n                      children: application.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"service-category\",\n                      children: application.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"application-status\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `status ${application.status}`,\n                      children: application.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"service-description\",\n                  children: application.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"service-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detail-item\",\n                    children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 388,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: application.location\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detail-item\",\n                    children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: new Date(application.startDate).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detail-item\",\n                    children: [/*#__PURE__*/_jsxDEV(User, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: application.clientName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 27\n                  }, this), application.proposedRate && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detail-item\",\n                    children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Proposed: $\", application.proposedRate, \"/hr\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"application-footer\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"applied-date\",\n                    children: [\"Applied: \", new Date(application.appliedAt).toLocaleDateString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 25\n                }, this)]\n              }, application.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: [/*#__PURE__*/_jsxDEV(Heart, {\n                size: 48\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"No applications yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Start applying to service requests to help others in your community.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this), activeTab === 'profile' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Profile Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-avatar\",\n                children: /*#__PURE__*/_jsxDEV(User, {\n                  size: 48\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: [user.firstName, \" \", user.lastName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"role-badge\",\n                  children: user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-secondary\",\n                children: \"Edit Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreateServiceRequest, {\n      isOpen: showCreateModal,\n      onClose: () => setShowCreateModal(false),\n      onSuccess: () => {\n        fetchMyRequests();\n        fetchServiceRequests();\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 221,\n    columnNumber: 5\n  }, this);\n};\n_s(UserDashboard, \"zwCs+kmnCaXj0msJxn+xUWhm9tY=\");\n_c = UserDashboard;\nexport default UserDashboard;\nvar _c;\n$RefreshReg$(_c, \"UserDashboard\");", "map": {"version": 3, "names": ["useState", "useEffect", "axios", "Plus", "Search", "Filter", "Calendar", "MapPin", "Clock", "DollarSign", "User", "Heart", "Star", "MessageCircle", "CheckCircle", "XCircle", "CreateServiceRequest", "ManageApplications", "jsxDEV", "_jsxDEV", "UserDashboard", "user", "_s", "activeTab", "setActiveTab", "serviceRequests", "setServiceRequests", "myRequests", "setMyRequests", "myApplications", "setMyApplications", "loading", "setLoading", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "showCreateModal", "setShowCreateModal", "showApplicationsModal", "setShowApplicationsModal", "selectedRequest", "setSelectedRequest", "fetchServiceRequests", "role", "fetchMyRequests", "fetchMyApplications", "token", "localStorage", "getItem", "params", "URLSearchParams", "category", "search", "limit", "response", "get", "headers", "Authorization", "data", "success", "requests", "error", "console", "handleApplyForService", "requestId", "post", "message", "alert", "_error$response", "_error$response$data", "handleViewApplications", "request", "categories", "filteredRequests", "filter", "matchesSearch", "title", "toLowerCase", "includes", "description", "matchesCategory", "ServiceRequestCard", "showActions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "hourlyRate", "location", "Date", "startDate", "toLocaleDateString", "endDate", "clientName", "fill", "clientRating", "applicationsCount", "status", "onClick", "id", "firstName", "TabButton", "label", "icon", "isActive", "type", "placeholder", "value", "onChange", "e", "target", "map", "length", "application", "proposedRate", "appliedAt", "lastName", "email", "isOpen", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Companion/frontend/src/components/UserDashboard.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Plus,\n  Search,\n  Filter,\n  Calendar,\n  MapPin,\n  Clock,\n  DollarSign,\n  User,\n  Heart,\n  Star,\n  MessageCircle,\n  CheckCircle,\n  XCircle\n} from 'lucide-react';\nimport CreateServiceRequest from './CreateServiceRequest';\nimport ManageApplications from './ManageApplications';\nimport './UserDashboard.css';\n\nconst UserDashboard = ({ user }) => {\n  const [activeTab, setActiveTab] = useState('browse');\n  const [serviceRequests, setServiceRequests] = useState([]);\n  const [myRequests, setMyRequests] = useState([]);\n  const [myApplications, setMyApplications] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showApplicationsModal, setShowApplicationsModal] = useState(false);\n  const [selectedRequest, setSelectedRequest] = useState(null);\n\n  // Fetch data from API\n  useEffect(() => {\n    fetchServiceRequests();\n    if (user.role === 'user') {\n      fetchMyRequests();\n    } else if (user.role === 'caregiver') {\n      fetchMyApplications();\n    }\n  }, [user.role, selectedCategory, searchTerm]);\n\n  const fetchServiceRequests = async () => {\n    try {\n      setLoading(true);\n      const token = localStorage.getItem('token');\n      const params = new URLSearchParams({\n        category: selectedCategory,\n        search: searchTerm,\n        limit: 20\n      });\n\n      const response = await axios.get(`http://localhost:5000/api/services/requests?${params}`, {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setServiceRequests(response.data.data.requests);\n      }\n    } catch (error) {\n      console.error('Error fetching service requests:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchMyRequests = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/services/my-requests', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setMyRequests(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching my requests:', error);\n    }\n  };\n\n  const fetchMyApplications = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/services/my-applications', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setMyApplications(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching my applications:', error);\n    }\n  };\n\n  const handleApplyForService = async (requestId) => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.post(\n        `http://localhost:5000/api/services/requests/${requestId}/apply`,\n        { message: 'I am interested in this opportunity and would love to help.' },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n\n      if (response.data.success) {\n        alert('Application submitted successfully!');\n        fetchServiceRequests(); // Refresh the list\n        fetchMyApplications(); // Refresh applications if caregiver\n      }\n    } catch (error) {\n      console.error('Error applying for service:', error);\n      alert(error.response?.data?.message || 'Failed to submit application');\n    }\n  };\n\n  const handleViewApplications = (request) => {\n    setSelectedRequest(request);\n    setShowApplicationsModal(true);\n  };\n\n  const categories = [\n    'all',\n    'Baby Care',\n    'Elderly Care',\n    'Disability Support',\n    'Reading & Writing Help',\n    'Household Help',\n    'Medical Assistance'\n  ];\n\n  const filteredRequests = serviceRequests.filter(request => {\n    const matchesSearch = request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         request.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || request.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const ServiceRequestCard = ({ request, showActions = true }) => (\n    <div className=\"service-card\">\n      <div className=\"service-header\">\n        <div className=\"service-title-section\">\n          <h3 className=\"service-title\">{request.title}</h3>\n          <span className=\"service-category\">{request.category}</span>\n        </div>\n        <div className=\"service-rate\">\n          <DollarSign size={16} />\n          <span>${request.hourlyRate}/hr</span>\n        </div>\n      </div>\n      \n      <p className=\"service-description\">{request.description}</p>\n      \n      <div className=\"service-details\">\n        <div className=\"detail-item\">\n          <MapPin size={16} />\n          <span>{request.location}</span>\n        </div>\n        <div className=\"detail-item\">\n          <Calendar size={16} />\n          <span>{new Date(request.startDate).toLocaleDateString()} - {new Date(request.endDate).toLocaleDateString()}</span>\n        </div>\n        <div className=\"detail-item\">\n          <User size={16} />\n          <span>{request.clientName}</span>\n          <div className=\"rating\">\n            <Star size={14} fill=\"currentColor\" />\n            <span>{request.clientRating}</span>\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"service-footer\">\n        <div className=\"service-stats\">\n          <span className=\"applications-count\">{request.applicationsCount} applications</span>\n          <span className={`status ${request.status}`}>{request.status}</span>\n        </div>\n        \n        {showActions && (\n          <div className=\"service-actions\">\n            {user.role === 'caregiver' ? (\n              <button\n                className=\"btn btn-primary\"\n                onClick={() => handleApplyForService(request.id)}\n              >\n                <Heart size={16} />\n                Apply Now\n              </button>\n            ) : user.role === 'user' && request.clientName && request.clientName.includes(user.firstName) ? (\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => handleViewApplications(request)}\n              >\n                <User size={16} />\n                View Applications ({request.applicationsCount})\n              </button>\n            ) : (\n              <button className=\"btn btn-secondary\">\n                <MessageCircle size={16} />\n                Contact\n              </button>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n\n  const TabButton = ({ id, label, icon, isActive, onClick }) => (\n    <button\n      className={`tab-button ${isActive ? 'active' : ''}`}\n      onClick={() => onClick(id)}\n    >\n      {icon}\n      <span>{label}</span>\n    </button>\n  );\n\n  return (\n    <div className=\"user-dashboard\">\n      <div className=\"dashboard-container\">\n        <div className=\"dashboard-header\">\n          <h1 className=\"dashboard-title\">\n            Welcome back, {user.firstName}!\n          </h1>\n          <p className=\"dashboard-subtitle\">\n            {user.role === 'caregiver' \n              ? 'Find meaningful opportunities to help others in your community'\n              : 'Connect with trusted caregivers for your needs'\n            }\n          </p>\n        </div>\n\n        <div className=\"dashboard-tabs\">\n          <TabButton\n            id=\"browse\"\n            label=\"Browse Services\"\n            icon={<Search size={20} />}\n            isActive={activeTab === 'browse'}\n            onClick={setActiveTab}\n          />\n          {user.role === 'user' && (\n            <TabButton\n              id=\"my-requests\"\n              label=\"My Requests\"\n              icon={<Calendar size={20} />}\n              isActive={activeTab === 'my-requests'}\n              onClick={setActiveTab}\n            />\n          )}\n          {user.role === 'caregiver' && (\n            <TabButton\n              id=\"my-applications\"\n              label=\"My Applications\"\n              icon={<Heart size={20} />}\n              isActive={activeTab === 'my-applications'}\n              onClick={setActiveTab}\n            />\n          )}\n          <TabButton\n            id=\"profile\"\n            label=\"Profile\"\n            icon={<User size={20} />}\n            isActive={activeTab === 'profile'}\n            onClick={setActiveTab}\n          />\n        </div>\n\n        <div className=\"dashboard-content\">\n          {activeTab === 'browse' && (\n            <div className=\"browse-section\">\n              <div className=\"browse-header\">\n                <div className=\"search-filters\">\n                  <div className=\"search-box\">\n                    <Search size={20} />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search services...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                    />\n                  </div>\n                  \n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className=\"category-filter\"\n                  >\n                    {categories.map(category => (\n                      <option key={category} value={category}>\n                        {category === 'all' ? 'All Categories' : category}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n                \n                {user.role === 'user' && (\n                  <button\n                    className=\"btn btn-primary\"\n                    onClick={() => setShowCreateModal(true)}\n                  >\n                    <Plus size={20} />\n                    Post New Request\n                  </button>\n                )}\n              </div>\n\n              <div className=\"services-grid\">\n                {loading ? (\n                  <div className=\"loading-state\">\n                    <div className=\"loading-spinner\"></div>\n                    <p>Loading opportunities...</p>\n                  </div>\n                ) : filteredRequests.length > 0 ? (\n                  filteredRequests.map(request => (\n                    <ServiceRequestCard key={request.id} request={request} />\n                  ))\n                ) : (\n                  <div className=\"empty-state\">\n                    <Search size={48} />\n                    <h3>No services found</h3>\n                    <p>Try adjusting your search criteria or check back later for new opportunities.</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'my-requests' && user.role === 'user' && (\n            <div className=\"my-requests-section\">\n              <div className=\"section-header\">\n                <h2>My Service Requests</h2>\n                <button\n                  className=\"btn btn-primary\"\n                  onClick={() => setShowCreateModal(true)}\n                >\n                  <Plus size={20} />\n                  Create New Request\n                </button>\n              </div>\n\n              <div className=\"requests-list\">\n                {myRequests.length > 0 ? (\n                  <div className=\"services-grid\">\n                    {myRequests.map(request => (\n                      <ServiceRequestCard key={request.id} request={request} showActions={false} />\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"empty-state\">\n                    <Calendar size={48} />\n                    <h3>No requests yet</h3>\n                    <p>Create your first service request to get started.</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'my-applications' && user.role === 'caregiver' && (\n            <div className=\"my-applications-section\">\n              <div className=\"section-header\">\n                <h2>My Applications</h2>\n              </div>\n\n              <div className=\"applications-list\">\n                {myApplications.length > 0 ? (\n                  <div className=\"services-grid\">\n                    {myApplications.map(application => (\n                      <div key={application.id} className=\"service-card application-card\">\n                        <div className=\"service-header\">\n                          <div className=\"service-title-section\">\n                            <h3 className=\"service-title\">{application.title}</h3>\n                            <span className=\"service-category\">{application.category}</span>\n                          </div>\n                          <div className=\"application-status\">\n                            <span className={`status ${application.status}`}>\n                              {application.status}\n                            </span>\n                          </div>\n                        </div>\n\n                        <p className=\"service-description\">{application.description}</p>\n\n                        <div className=\"service-details\">\n                          <div className=\"detail-item\">\n                            <MapPin size={16} />\n                            <span>{application.location}</span>\n                          </div>\n                          <div className=\"detail-item\">\n                            <Calendar size={16} />\n                            <span>{new Date(application.startDate).toLocaleDateString()}</span>\n                          </div>\n                          <div className=\"detail-item\">\n                            <User size={16} />\n                            <span>{application.clientName}</span>\n                          </div>\n                          {application.proposedRate && (\n                            <div className=\"detail-item\">\n                              <DollarSign size={16} />\n                              <span>Proposed: ${application.proposedRate}/hr</span>\n                            </div>\n                          )}\n                        </div>\n\n                        <div className=\"application-footer\">\n                          <small className=\"applied-date\">\n                            Applied: {new Date(application.appliedAt).toLocaleDateString()}\n                          </small>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"empty-state\">\n                    <Heart size={48} />\n                    <h3>No applications yet</h3>\n                    <p>Start applying to service requests to help others in your community.</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'profile' && (\n            <div className=\"profile-section\">\n              <div className=\"section-header\">\n                <h2>Profile Settings</h2>\n              </div>\n              \n              <div className=\"profile-content\">\n                <div className=\"profile-card\">\n                  <div className=\"profile-avatar\">\n                    <User size={48} />\n                  </div>\n                  <div className=\"profile-info\">\n                    <h3>{user.firstName} {user.lastName}</h3>\n                    <p>{user.email}</p>\n                    <span className=\"role-badge\">{user.role}</span>\n                  </div>\n                  <button className=\"btn btn-secondary\">Edit Profile</button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      <CreateServiceRequest\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        onSuccess={() => {\n          fetchMyRequests();\n          fetchServiceRequests();\n        }}\n      />\n    </div>\n  );\n};\n\nexport default UserDashboard;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,aAAa,EACbC,WAAW,EACXC,OAAO,QACF,cAAc;AACrB,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd0C,oBAAoB,CAAC,CAAC;IACtB,IAAItB,IAAI,CAACuB,IAAI,KAAK,MAAM,EAAE;MACxBC,eAAe,CAAC,CAAC;IACnB,CAAC,MAAM,IAAIxB,IAAI,CAACuB,IAAI,KAAK,WAAW,EAAE;MACpCE,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACzB,IAAI,CAACuB,IAAI,EAAET,gBAAgB,EAAEF,UAAU,CAAC,CAAC;EAE7C,MAAMU,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMe,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCC,QAAQ,EAAEjB,gBAAgB;QAC1BkB,MAAM,EAAEpB,UAAU;QAClBqB,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,MAAMC,QAAQ,GAAG,MAAMrD,KAAK,CAACsD,GAAG,CAAC,+CAA+CN,MAAM,EAAE,EAAE;QACxFO,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUX,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIQ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBlC,kBAAkB,CAAC6B,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACE,QAAQ,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMM,QAAQ,GAAG,MAAMrD,KAAK,CAACsD,GAAG,CAAC,gDAAgD,EAAE;QACjFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUX,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIQ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBhC,aAAa,CAAC2B,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;MACnC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMhB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMM,QAAQ,GAAG,MAAMrD,KAAK,CAACsD,GAAG,CAAC,oDAAoD,EAAE;QACrFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUX,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIQ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzB9B,iBAAiB,CAACyB,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;MACvC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAME,qBAAqB,GAAG,MAAOC,SAAS,IAAK;IACjD,IAAI;MACF,MAAMlB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMM,QAAQ,GAAG,MAAMrD,KAAK,CAACgE,IAAI,CAC/B,+CAA+CD,SAAS,QAAQ,EAChE;QAAEE,OAAO,EAAE;MAA8D,CAAC,EAC1E;QAAEV,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUX,KAAK;QAAG;MAAE,CAClD,CAAC;MAED,IAAIQ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBQ,KAAK,CAAC,qCAAqC,CAAC;QAC5CzB,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACxBG,mBAAmB,CAAC,CAAC,CAAC,CAAC;MACzB;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MAAA,IAAAO,eAAA,EAAAC,oBAAA;MACdP,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDM,KAAK,CAAC,EAAAC,eAAA,GAAAP,KAAK,CAACP,QAAQ,cAAAc,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBH,OAAO,KAAI,8BAA8B,CAAC;IACxE;EACF,CAAC;EAED,MAAMI,sBAAsB,GAAIC,OAAO,IAAK;IAC1C9B,kBAAkB,CAAC8B,OAAO,CAAC;IAC3BhC,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAMiC,UAAU,GAAG,CACjB,KAAK,EACL,WAAW,EACX,cAAc,EACd,oBAAoB,EACpB,wBAAwB,EACxB,gBAAgB,EAChB,oBAAoB,CACrB;EAED,MAAMC,gBAAgB,GAAGjD,eAAe,CAACkD,MAAM,CAACH,OAAO,IAAI;IACzD,MAAMI,aAAa,GAAGJ,OAAO,CAACK,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC,IAC/DN,OAAO,CAACQ,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC;IACzF,MAAMG,eAAe,GAAG9C,gBAAgB,KAAK,KAAK,IAAIqC,OAAO,CAACpB,QAAQ,KAAKjB,gBAAgB;IAC3F,OAAOyC,aAAa,IAAIK,eAAe;EACzC,CAAC,CAAC;EAEF,MAAMC,kBAAkB,GAAGA,CAAC;IAAEV,OAAO;IAAEW,WAAW,GAAG;EAAK,CAAC,kBACzDhE,OAAA;IAAKiE,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BlE,OAAA;MAAKiE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BlE,OAAA;QAAKiE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpClE,OAAA;UAAIiE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEb,OAAO,CAACK;QAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClDtE,OAAA;UAAMiE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAEb,OAAO,CAACpB;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACNtE,OAAA;QAAKiE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BlE,OAAA,CAACV,UAAU;UAACiF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxBtE,OAAA;UAAAkE,QAAA,GAAM,GAAC,EAACb,OAAO,CAACmB,UAAU,EAAC,KAAG;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtE,OAAA;MAAGiE,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EAAEb,OAAO,CAACQ;IAAW;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE5DtE,OAAA;MAAKiE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BlE,OAAA;QAAKiE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlE,OAAA,CAACZ,MAAM;UAACmF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpBtE,OAAA;UAAAkE,QAAA,EAAOb,OAAO,CAACoB;QAAQ;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACNtE,OAAA;QAAKiE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlE,OAAA,CAACb,QAAQ;UAACoF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtBtE,OAAA;UAAAkE,QAAA,GAAO,IAAIQ,IAAI,CAACrB,OAAO,CAACsB,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAC,KAAG,EAAC,IAAIF,IAAI,CAACrB,OAAO,CAACwB,OAAO,CAAC,CAACD,kBAAkB,CAAC,CAAC;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/G,CAAC,eACNtE,OAAA;QAAKiE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlE,OAAA,CAACT,IAAI;UAACgF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClBtE,OAAA;UAAAkE,QAAA,EAAOb,OAAO,CAACyB;QAAU;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjCtE,OAAA;UAAKiE,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrBlE,OAAA,CAACP,IAAI;YAAC8E,IAAI,EAAE,EAAG;YAACQ,IAAI,EAAC;UAAc;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtCtE,OAAA;YAAAkE,QAAA,EAAOb,OAAO,CAAC2B;UAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtE,OAAA;MAAKiE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BlE,OAAA;QAAKiE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BlE,OAAA;UAAMiE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAEb,OAAO,CAAC4B,iBAAiB,EAAC,eAAa;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFtE,OAAA;UAAMiE,SAAS,EAAE,UAAUZ,OAAO,CAAC6B,MAAM,EAAG;UAAAhB,QAAA,EAAEb,OAAO,CAAC6B;QAAM;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,EAELN,WAAW,iBACVhE,OAAA;QAAKiE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7BhE,IAAI,CAACuB,IAAI,KAAK,WAAW,gBACxBzB,OAAA;UACEiE,SAAS,EAAC,iBAAiB;UAC3BkB,OAAO,EAAEA,CAAA,KAAMtC,qBAAqB,CAACQ,OAAO,CAAC+B,EAAE,CAAE;UAAAlB,QAAA,gBAEjDlE,OAAA,CAACR,KAAK;YAAC+E,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAErB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,GACPpE,IAAI,CAACuB,IAAI,KAAK,MAAM,IAAI4B,OAAO,CAACyB,UAAU,IAAIzB,OAAO,CAACyB,UAAU,CAAClB,QAAQ,CAAC1D,IAAI,CAACmF,SAAS,CAAC,gBAC3FrF,OAAA;UACEiE,SAAS,EAAC,mBAAmB;UAC7BkB,OAAO,EAAEA,CAAA,KAAM/B,sBAAsB,CAACC,OAAO,CAAE;UAAAa,QAAA,gBAE/ClE,OAAA,CAACT,IAAI;YAACgF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBACC,EAACjB,OAAO,CAAC4B,iBAAiB,EAAC,GAChD;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETtE,OAAA;UAAQiE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACnClE,OAAA,CAACN,aAAa;YAAC6E,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAE7B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMgB,SAAS,GAAGA,CAAC;IAAEF,EAAE;IAAEG,KAAK;IAAEC,IAAI;IAAEC,QAAQ;IAAEN;EAAQ,CAAC,kBACvDnF,OAAA;IACEiE,SAAS,EAAE,cAAcwB,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;IACpDN,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAACC,EAAE,CAAE;IAAAlB,QAAA,GAE1BsB,IAAI,eACLxF,OAAA;MAAAkE,QAAA,EAAOqB;IAAK;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CACT;EAED,oBACEtE,OAAA;IAAKiE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BlE,OAAA;MAAKiE,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClClE,OAAA;QAAKiE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BlE,OAAA;UAAIiE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,gBAChB,EAAChE,IAAI,CAACmF,SAAS,EAAC,GAChC;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtE,OAAA;UAAGiE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC9BhE,IAAI,CAACuB,IAAI,KAAK,WAAW,GACtB,gEAAgE,GAChE;QAAgD;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BlE,OAAA,CAACsF,SAAS;UACRF,EAAE,EAAC,QAAQ;UACXG,KAAK,EAAC,iBAAiB;UACvBC,IAAI,eAAExF,OAAA,CAACf,MAAM;YAACsF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BmB,QAAQ,EAAErF,SAAS,KAAK,QAAS;UACjC+E,OAAO,EAAE9E;QAAa;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,EACDpE,IAAI,CAACuB,IAAI,KAAK,MAAM,iBACnBzB,OAAA,CAACsF,SAAS;UACRF,EAAE,EAAC,aAAa;UAChBG,KAAK,EAAC,aAAa;UACnBC,IAAI,eAAExF,OAAA,CAACb,QAAQ;YAACoF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BmB,QAAQ,EAAErF,SAAS,KAAK,aAAc;UACtC+E,OAAO,EAAE9E;QAAa;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACF,EACApE,IAAI,CAACuB,IAAI,KAAK,WAAW,iBACxBzB,OAAA,CAACsF,SAAS;UACRF,EAAE,EAAC,iBAAiB;UACpBG,KAAK,EAAC,iBAAiB;UACvBC,IAAI,eAAExF,OAAA,CAACR,KAAK;YAAC+E,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BmB,QAAQ,EAAErF,SAAS,KAAK,iBAAkB;UAC1C+E,OAAO,EAAE9E;QAAa;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACF,eACDtE,OAAA,CAACsF,SAAS;UACRF,EAAE,EAAC,SAAS;UACZG,KAAK,EAAC,SAAS;UACfC,IAAI,eAAExF,OAAA,CAACT,IAAI;YAACgF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBmB,QAAQ,EAAErF,SAAS,KAAK,SAAU;UAClC+E,OAAO,EAAE9E;QAAa;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAC/B9D,SAAS,KAAK,QAAQ,iBACrBJ,OAAA;UAAKiE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BlE,OAAA;YAAKiE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlE,OAAA;cAAKiE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlE,OAAA;gBAAKiE,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlE,OAAA,CAACf,MAAM;kBAACsF,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpBtE,OAAA;kBACE0F,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,oBAAoB;kBAChCC,KAAK,EAAE9E,UAAW;kBAClB+E,QAAQ,EAAGC,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtE,OAAA;gBACE4F,KAAK,EAAE5E,gBAAiB;gBACxB6E,QAAQ,EAAGC,CAAC,IAAK7E,mBAAmB,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACrD3B,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAE1BZ,UAAU,CAAC0C,GAAG,CAAC/D,QAAQ,iBACtBjC,OAAA;kBAAuB4F,KAAK,EAAE3D,QAAS;kBAAAiC,QAAA,EACpCjC,QAAQ,KAAK,KAAK,GAAG,gBAAgB,GAAGA;gBAAQ,GADtCA,QAAQ;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELpE,IAAI,CAACuB,IAAI,KAAK,MAAM,iBACnBzB,OAAA;cACEiE,SAAS,EAAC,iBAAiB;cAC3BkB,OAAO,EAAEA,CAAA,KAAMhE,kBAAkB,CAAC,IAAI,CAAE;cAAA+C,QAAA,gBAExClE,OAAA,CAAChB,IAAI;gBAACuF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BtD,OAAO,gBACNZ,OAAA;cAAKiE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BlE,OAAA;gBAAKiE,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvCtE,OAAA;gBAAAkE,QAAA,EAAG;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,GACJf,gBAAgB,CAAC0C,MAAM,GAAG,CAAC,GAC7B1C,gBAAgB,CAACyC,GAAG,CAAC3C,OAAO,iBAC1BrD,OAAA,CAAC+D,kBAAkB;cAAkBV,OAAO,EAAEA;YAAQ,GAA7BA,OAAO,CAAC+B,EAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqB,CACzD,CAAC,gBAEFtE,OAAA;cAAKiE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlE,OAAA,CAACf,MAAM;gBAACsF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBtE,OAAA;gBAAAkE,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BtE,OAAA;gBAAAkE,QAAA,EAAG;cAA6E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAlE,SAAS,KAAK,aAAa,IAAIF,IAAI,CAACuB,IAAI,KAAK,MAAM,iBAClDzB,OAAA;UAAKiE,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClClE,OAAA;YAAKiE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BlE,OAAA;cAAAkE,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BtE,OAAA;cACEiE,SAAS,EAAC,iBAAiB;cAC3BkB,OAAO,EAAEA,CAAA,KAAMhE,kBAAkB,CAAC,IAAI,CAAE;cAAA+C,QAAA,gBAExClE,OAAA,CAAChB,IAAI;gBAACuF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3B1D,UAAU,CAACyF,MAAM,GAAG,CAAC,gBACpBjG,OAAA;cAAKiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3B1D,UAAU,CAACwF,GAAG,CAAC3C,OAAO,iBACrBrD,OAAA,CAAC+D,kBAAkB;gBAAkBV,OAAO,EAAEA,OAAQ;gBAACW,WAAW,EAAE;cAAM,GAAjDX,OAAO,CAAC+B,EAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAyC,CAC7E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAENtE,OAAA;cAAKiE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlE,OAAA,CAACb,QAAQ;gBAACoF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtBtE,OAAA;gBAAAkE,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBtE,OAAA;gBAAAkE,QAAA,EAAG;cAAiD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAlE,SAAS,KAAK,iBAAiB,IAAIF,IAAI,CAACuB,IAAI,KAAK,WAAW,iBAC3DzB,OAAA;UAAKiE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtClE,OAAA;YAAKiE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BlE,OAAA;cAAAkE,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC/BxD,cAAc,CAACuF,MAAM,GAAG,CAAC,gBACxBjG,OAAA;cAAKiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3BxD,cAAc,CAACsF,GAAG,CAACE,WAAW,iBAC7BlG,OAAA;gBAA0BiE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBACjElE,OAAA;kBAAKiE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BlE,OAAA;oBAAKiE,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpClE,OAAA;sBAAIiE,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEgC,WAAW,CAACxC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtDtE,OAAA;sBAAMiE,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEgC,WAAW,CAACjE;oBAAQ;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNtE,OAAA;oBAAKiE,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,eACjClE,OAAA;sBAAMiE,SAAS,EAAE,UAAUiC,WAAW,CAAChB,MAAM,EAAG;sBAAAhB,QAAA,EAC7CgC,WAAW,CAAChB;oBAAM;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENtE,OAAA;kBAAGiE,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAEgC,WAAW,CAACrC;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAEhEtE,OAAA;kBAAKiE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BlE,OAAA;oBAAKiE,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BlE,OAAA,CAACZ,MAAM;sBAACmF,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACpBtE,OAAA;sBAAAkE,QAAA,EAAOgC,WAAW,CAACzB;oBAAQ;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACNtE,OAAA;oBAAKiE,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BlE,OAAA,CAACb,QAAQ;sBAACoF,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtBtE,OAAA;sBAAAkE,QAAA,EAAO,IAAIQ,IAAI,CAACwB,WAAW,CAACvB,SAAS,CAAC,CAACC,kBAAkB,CAAC;oBAAC;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACNtE,OAAA;oBAAKiE,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BlE,OAAA,CAACT,IAAI;sBAACgF,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClBtE,OAAA;sBAAAkE,QAAA,EAAOgC,WAAW,CAACpB;oBAAU;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,EACL4B,WAAW,CAACC,YAAY,iBACvBnG,OAAA;oBAAKiE,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BlE,OAAA,CAACV,UAAU;sBAACiF,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACxBtE,OAAA;sBAAAkE,QAAA,GAAM,aAAW,EAACgC,WAAW,CAACC,YAAY,EAAC,KAAG;oBAAA;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENtE,OAAA;kBAAKiE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,eACjClE,OAAA;oBAAOiE,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAAC,WACrB,EAAC,IAAIQ,IAAI,CAACwB,WAAW,CAACE,SAAS,CAAC,CAACxB,kBAAkB,CAAC,CAAC;kBAAA;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAxCE4B,WAAW,CAACd,EAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyCnB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAENtE,OAAA;cAAKiE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlE,OAAA,CAACR,KAAK;gBAAC+E,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnBtE,OAAA;gBAAAkE,QAAA,EAAI;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BtE,OAAA;gBAAAkE,QAAA,EAAG;cAAoE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAlE,SAAS,KAAK,SAAS,iBACtBJ,OAAA;UAAKiE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BlE,OAAA;YAAKiE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BlE,OAAA;cAAAkE,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BlE,OAAA;cAAKiE,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlE,OAAA;gBAAKiE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7BlE,OAAA,CAACT,IAAI;kBAACgF,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BlE,OAAA;kBAAAkE,QAAA,GAAKhE,IAAI,CAACmF,SAAS,EAAC,GAAC,EAACnF,IAAI,CAACmG,QAAQ;gBAAA;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzCtE,OAAA;kBAAAkE,QAAA,EAAIhE,IAAI,CAACoG;gBAAK;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnBtE,OAAA;kBAAMiE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEhE,IAAI,CAACuB;gBAAI;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNtE,OAAA;gBAAQiE,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtE,OAAA,CAACH,oBAAoB;MACnB0G,MAAM,EAAErF,eAAgB;MACxBsF,OAAO,EAAEA,CAAA,KAAMrF,kBAAkB,CAAC,KAAK,CAAE;MACzCsF,SAAS,EAAEA,CAAA,KAAM;QACf/E,eAAe,CAAC,CAAC;QACjBF,oBAAoB,CAAC,CAAC;MACxB;IAAE;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnE,EAAA,CAtbIF,aAAa;AAAAyG,EAAA,GAAbzG,aAAa;AAwbnB,eAAeA,aAAa;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}