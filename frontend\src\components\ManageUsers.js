import { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  UserCheck, 
  UserX, 
  Trash2, 
  Shield, 
  User,
  Mail,
  Calendar,
  AlertTriangle
} from 'lucide-react';
import axios from 'axios';
import './ManageUsers.css';

const ManageUsers = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });

  useEffect(() => {
    fetchUsers();
  }, [pagination.page, roleFilter, searchTerm, statusFilter]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        page: pagination.page,
        limit: pagination.limit,
        role: roleFilter,
        search: searchTerm
      });

      const response = await axios.get(`http://localhost:5000/api/admin/users?${params}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setUsers(response.data.data.users);
        setPagination(prev => ({
          ...prev,
          ...response.data.data.pagination
        }));
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUserAction = async (userId, action, value = true) => {
    try {
      const token = localStorage.getItem('token');
      
      if (action === 'delete') {
        if (!window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
          return;
        }
        
        await axios.delete(`http://localhost:5000/api/admin/users/${userId}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        alert('User deleted successfully');
      } else {
        const updateData = {};
        if (action === 'verify') updateData.isVerified = value;
        if (action === 'block') updateData.isBlocked = value;
        
        await axios.patch(`http://localhost:5000/api/admin/users/${userId}/status`, 
          updateData,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        
        const actionText = action === 'verify' 
          ? (value ? 'approved' : 'unapproved') 
          : (value ? 'blocked' : 'unblocked');
        alert(`User ${actionText} successfully`);
      }
      
      fetchUsers(); // Refresh the list
    } catch (error) {
      console.error(`Error performing ${action}:`, error);
      alert(error.response?.data?.message || `Failed to ${action} user`);
    }
  };

  const getStatusBadge = (user) => {
    if (user.isBlocked) return { text: 'Blocked', class: 'blocked' };
    if (user.isVerified) return { text: 'Active', class: 'active' };
    return { text: 'Pending', class: 'pending' };
  };

  const filteredUsers = users.filter(user => {
    if (statusFilter !== 'all') {
      const status = getStatusBadge(user);
      if (statusFilter !== status.text.toLowerCase()) return false;
    }
    return true;
  });

  return (
    <div className="manage-users">
      <div className="manage-users-header">
        <h2>Manage Users</h2>
        <p>Comprehensive user management with approval, blocking, and deletion capabilities</p>
      </div>

      {/* Filters */}
      <div className="filters-section">
        <div className="search-box">
          <Search size={20} />
          <input
            type="text"
            placeholder="Search users by name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <select
          value={roleFilter}
          onChange={(e) => setRoleFilter(e.target.value)}
          className="filter-select"
        >
          <option value="all">All Roles</option>
          <option value="user">Users</option>
          <option value="caregiver">Caregivers</option>
        </select>

        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="filter-select"
        >
          <option value="all">All Status</option>
          <option value="pending">Pending</option>
          <option value="active">Active</option>
          <option value="blocked">Blocked</option>
        </select>
      </div>

      {/* Users Table */}
      <div className="users-table-container">
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading users...</p>
          </div>
        ) : (
          <table className="users-table">
            <thead>
              <tr>
                <th>User</th>
                <th>Role</th>
                <th>Status</th>
                <th>Joined</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.length > 0 ? (
                filteredUsers.map(user => {
                  const status = getStatusBadge(user);
                  return (
                    <tr key={user.id}>
                      <td className="user-info">
                        <div className="user-avatar">
                          <User size={20} />
                        </div>
                        <div className="user-details">
                          <div className="user-name">{user.name}</div>
                          <div className="user-email">
                            <Mail size={14} />
                            {user.email}
                          </div>
                        </div>
                      </td>
                      
                      <td>
                        <span className={`role-badge ${user.role}`}>
                          {user.role}
                        </span>
                      </td>
                      
                      <td>
                        <span className={`status-badge ${status.class}`}>
                          {status.text}
                        </span>
                      </td>
                      
                      <td className="join-date">
                        <Calendar size={14} />
                        {new Date(user.createdAt).toLocaleDateString()}
                      </td>
                      
                      <td className="user-actions">
                        {!user.isBlocked && (
                          <>
                            {!user.isVerified ? (
                              <button
                                className="action-btn approve"
                                onClick={() => handleUserAction(user.id, 'verify', true)}
                                title="Approve User"
                              >
                                <UserCheck size={16} />
                              </button>
                            ) : (
                              <button
                                className="action-btn unapprove"
                                onClick={() => handleUserAction(user.id, 'verify', false)}
                                title="Unapprove User"
                              >
                                <UserX size={16} />
                              </button>
                            )}
                            
                            <button
                              className="action-btn block"
                              onClick={() => handleUserAction(user.id, 'block', true)}
                              title="Block User"
                            >
                              <Shield size={16} />
                            </button>
                          </>
                        )}
                        
                        {user.isBlocked && (
                          <button
                            className="action-btn unblock"
                            onClick={() => handleUserAction(user.id, 'block', false)}
                            title="Unblock User"
                          >
                            <UserCheck size={16} />
                          </button>
                        )}
                        
                        <button
                          className="action-btn delete"
                          onClick={() => handleUserAction(user.id, 'delete')}
                          title="Delete User"
                        >
                          <Trash2 size={16} />
                        </button>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan="5" className="no-data">
                    No users found matching your criteria
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        )}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="pagination">
          <button
            className="pagination-btn"
            disabled={pagination.page === 1}
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
          >
            Previous
          </button>
          
          <span className="pagination-info">
            Page {pagination.page} of {pagination.pages} ({pagination.total} users)
          </span>
          
          <button
            className="pagination-btn"
            disabled={pagination.page === pagination.pages}
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
          >
            Next
          </button>
        </div>
      )}

      {/* Info Box */}
      <div className="info-box">
        <AlertTriangle size={20} />
        <div>
          <strong>User Management Guide:</strong>
          <ul>
            <li><strong>Approve:</strong> Allow pending users to login and access the platform</li>
            <li><strong>Block:</strong> Prevent users from logging in (reversible)</li>
            <li><strong>Delete:</strong> Permanently remove user and all their data (irreversible)</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ManageUsers;
