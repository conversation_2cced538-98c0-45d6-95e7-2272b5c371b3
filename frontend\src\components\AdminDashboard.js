import { useState, useEffect } from 'react';
import axios from 'axios';
import { Users, Heart, Activity, TrendingUp, UserCheck, UserX } from 'lucide-react';
import ManageUsers from './ManageUsers';
import './AdminDashboard.css';

const AdminDashboard = ({ user }) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalCaregivers: 0,
    activeRequests: 0,
    completedServices: 0
  });
  const [recentUsers, setRecentUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAdminStats();
    fetchRecentUsers();
  }, []);

  const fetchAdminStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5000/api/admin/stats', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching admin stats:', error);
    }
  };

  const fetchRecentUsers = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5000/api/admin/recent-users?limit=5', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setRecentUsers(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching recent users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUserStatusUpdate = async (userId, isVerified) => {
    try {
      const token = localStorage.getItem('token');
      await axios.patch(`http://localhost:5000/api/admin/users/${userId}/status`,
        { isVerified },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      // Refresh the users list
      fetchRecentUsers();
      fetchAdminStats();
    } catch (error) {
      console.error('Error updating user status:', error);
      alert('Failed to update user status');
    }
  };



  const StatCard = ({ icon, title, value, color }) => (
    <div className="stat-card">
      <div className={`stat-icon ${color}`}>
        {icon}
      </div>
      <div className="stat-content">
        <h3 className="stat-value">{value}</h3>
        <p className="stat-title">{title}</p>
      </div>
    </div>
  );

  return (
    <div className="admin-dashboard">
      <div className="dashboard-container">
        <div className="dashboard-header">
          <h1 className="dashboard-title">Admin Dashboard</h1>
          <p className="dashboard-subtitle">
            Welcome back, {user.firstName}! Here's what's happening in your community.
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="admin-tabs">
          <button
            className={`tab-button ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => setActiveTab('dashboard')}
          >
            <Activity size={20} />
            Dashboard
          </button>
          <button
            className={`tab-button ${activeTab === 'users' ? 'active' : ''}`}
            onClick={() => setActiveTab('users')}
          >
            <Users size={20} />
            Manage Users
          </button>
        </div>

        {/* Dashboard Content */}
        {activeTab === 'dashboard' && (
          <>
            {/* Stats Grid */}
            <div className="stats-grid">
          <StatCard
            icon={<Users size={24} />}
            title="Total Users"
            value={stats.totalUsers}
            color="blue"
          />
          <StatCard
            icon={<Heart size={24} />}
            title="Caregivers"
            value={stats.totalCaregivers}
            color="pink"
          />
          <StatCard
            icon={<Activity size={24} />}
            title="Active Requests"
            value={stats.activeRequests}
            color="orange"
          />
          <StatCard
            icon={<TrendingUp size={24} />}
            title="Completed Services"
            value={stats.completedServices}
            color="green"
          />
        </div>

        {/* Recent Users Table */}
        <div className="dashboard-section">
          <h2 className="section-title">Recent Users</h2>
          <div className="section-info">
            <p><strong>User Verification System:</strong> New users must be approved by admin before they can access the platform.
            Pending users can register but cannot login until verified.</p>
          </div>
          {loading ? (
            <div className="loading-state">
              <div className="loading-spinner"></div>
              <p>Loading users...</p>
            </div>
          ) : (
            <div className="table-container">
              <table className="users-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {recentUsers.length > 0 ? (
                    recentUsers.map(user => (
                      <tr key={user.id}>
                        <td className="user-name">{user.name}</td>
                        <td className="user-email">{user.email}</td>
                        <td>
                          <span className={`role-badge ${user.role}`}>
                            {user.role}
                          </span>
                        </td>
                        <td>
                          <span className={`status-badge ${user.status}`}>
                            {user.status}
                          </span>
                        </td>
                        <td className="user-actions">
                          {user.status === 'pending' ? (
                            <button
                              className="action-btn approve"
                              onClick={() => handleUserStatusUpdate(user.id, true)}
                              title="Approve User"
                            >
                              <UserCheck size={16} />
                            </button>
                          ) : (
                            <button
                              className="action-btn reject"
                              onClick={() => handleUserStatusUpdate(user.id, false)}
                              title="Suspend User"
                            >
                              <UserX size={16} />
                            </button>
                          )}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="5" className="no-data">
                        No users found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="dashboard-section">
          <h2 className="section-title">Quick Actions</h2>
          <div className="quick-actions">
            <button className="action-card">
              <Users size={24} />
              <span>Manage Users</span>
            </button>
            <button className="action-card">
              <Heart size={24} />
              <span>Review Services</span>
            </button>
            <button className="action-card">
              <Activity size={24} />
              <span>View Reports</span>
            </button>
            <button className="action-card">
              <TrendingUp size={24} />
              <span>Analytics</span>
            </button>
          </div>
        </div>
          </>
        )}

        {/* Manage Users Tab */}
        {activeTab === 'users' && (
          <ManageUsers />
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
