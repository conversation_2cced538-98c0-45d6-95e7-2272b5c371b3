import { useState, useEffect } from 'react';
import { Users, Heart, Activity, TrendingUp, UserCheck, UserX } from 'lucide-react';
import './AdminDashboard.css';

const AdminDashboard = ({ user }) => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalCaregivers: 0,
    activeRequests: 0,
    completedServices: 0
  });

  // Mock data for demonstration
  useEffect(() => {
    // In a real app, you would fetch this data from your API
    setStats({
      totalUsers: 156,
      totalCaregivers: 42,
      activeRequests: 23,
      completedServices: 189
    });
  }, []);

  const recentUsers = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'user', status: 'active' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'caregiver', status: 'active' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'user', status: 'pending' },
    { id: 4, name: '<PERSON>', email: '<EMAIL>', role: 'caregiver', status: 'active' },
  ];

  const StatCard = ({ icon, title, value, color }) => (
    <div className="stat-card">
      <div className={`stat-icon ${color}`}>
        {icon}
      </div>
      <div className="stat-content">
        <h3 className="stat-value">{value}</h3>
        <p className="stat-title">{title}</p>
      </div>
    </div>
  );

  return (
    <div className="admin-dashboard">
      <div className="dashboard-container">
        <div className="dashboard-header">
          <h1 className="dashboard-title">Admin Dashboard</h1>
          <p className="dashboard-subtitle">
            Welcome back, {user.firstName}! Here's what's happening in your community.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="stats-grid">
          <StatCard
            icon={<Users size={24} />}
            title="Total Users"
            value={stats.totalUsers}
            color="blue"
          />
          <StatCard
            icon={<Heart size={24} />}
            title="Caregivers"
            value={stats.totalCaregivers}
            color="pink"
          />
          <StatCard
            icon={<Activity size={24} />}
            title="Active Requests"
            value={stats.activeRequests}
            color="orange"
          />
          <StatCard
            icon={<TrendingUp size={24} />}
            title="Completed Services"
            value={stats.completedServices}
            color="green"
          />
        </div>

        {/* Recent Users Table */}
        <div className="dashboard-section">
          <h2 className="section-title">Recent Users</h2>
          <div className="table-container">
            <table className="users-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {recentUsers.map(user => (
                  <tr key={user.id}>
                    <td className="user-name">{user.name}</td>
                    <td className="user-email">{user.email}</td>
                    <td>
                      <span className={`role-badge ${user.role}`}>
                        {user.role}
                      </span>
                    </td>
                    <td>
                      <span className={`status-badge ${user.status}`}>
                        {user.status}
                      </span>
                    </td>
                    <td className="user-actions">
                      <button className="action-btn approve">
                        <UserCheck size={16} />
                      </button>
                      <button className="action-btn reject">
                        <UserX size={16} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="dashboard-section">
          <h2 className="section-title">Quick Actions</h2>
          <div className="quick-actions">
            <button className="action-card">
              <Users size={24} />
              <span>Manage Users</span>
            </button>
            <button className="action-card">
              <Heart size={24} />
              <span>Review Services</span>
            </button>
            <button className="action-card">
              <Activity size={24} />
              <span>View Reports</span>
            </button>
            <button className="action-card">
              <TrendingUp size={24} />
              <span>Analytics</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
