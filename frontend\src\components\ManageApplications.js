import { useState, useEffect } from 'react';
import { X, User, Star, Calendar, MessageCircle, Check, XCircle, IndianRupee } from 'lucide-react';
import axios from 'axios';
import './ManageApplications.css';

const ManageApplications = ({ isOpen, onClose, requestId, requestTitle }) => {
  const [applications, setApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen && requestId) {
      fetchApplications();
    }
  }, [isOpen, requestId]);

  const fetchApplications = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.get(
        `http://localhost:5000/api/services/requests/${requestId}/applications`,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.data.success) {
        setApplications(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
      setError('Failed to load applications');
    } finally {
      setLoading(false);
    }
  };

  const handleApplicationStatus = async (applicationId, status) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.patch(
        `http://localhost:5000/api/services/applications/${applicationId}/status`,
        { status },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.data.success) {
        // Refresh applications
        fetchApplications();
        alert(`Application ${status} successfully!`);
      }
    } catch (error) {
      console.error('Error updating application status:', error);
      alert(error.response?.data?.message || 'Failed to update application status');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content applications-modal">
        <div className="modal-header">
          <div>
            <h2>Manage Applications</h2>
            <p className="request-title">{requestTitle}</p>
          </div>
          <button className="close-button" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        <div className="modal-body">
          {loading ? (
            <div className="loading-state">
              <div className="loading-spinner"></div>
              <p>Loading applications...</p>
            </div>
          ) : error ? (
            <div className="error-state">
              <p>{error}</p>
            </div>
          ) : applications.length === 0 ? (
            <div className="empty-state">
              <User size={48} />
              <h3>No Applications Yet</h3>
              <p>No caregivers have applied for this service request yet.</p>
            </div>
          ) : (
            <div className="applications-list">
              {applications.map(application => (
                <div key={application.id} className="application-card">
                  <div className="application-header">
                    <div className="caregiver-info">
                      <div className="caregiver-avatar">
                        <User size={24} />
                      </div>
                      <div className="caregiver-details">
                        <h3 className="caregiver-name">{application.caregiverName}</h3>
                        <p className="caregiver-email">{application.caregiverEmail}</p>
                        {application.experienceYears > 0 && (
                          <p className="experience">
                            {application.experienceYears} years of experience
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="application-status">
                      <span className={`status-badge ${application.status}`}>
                        {application.status}
                      </span>
                    </div>
                  </div>

                  {application.rating > 0 && (
                    <div className="rating-section">
                      <div className="rating">
                        <Star size={16} fill="currentColor" />
                        <span>{application.rating.toFixed(1)}</span>
                        <span className="reviews-count">
                          ({application.totalReviews} reviews)
                        </span>
                      </div>
                    </div>
                  )}

                  {application.proposedRate && (
                    <div className="proposed-rate">
                      <IndianRupee size={16} />
                      <span>Proposed Rate: ₹{application.proposedRate}/hr</span>
                    </div>
                  )}

                  {application.message && (
                    <div className="application-message">
                      <MessageCircle size={16} />
                      <div className="message-content">
                        <strong>Message:</strong>
                        <p>{application.message}</p>
                      </div>
                    </div>
                  )}

                  {application.bio && (
                    <div className="caregiver-bio">
                      <strong>About:</strong>
                      <p>{application.bio}</p>
                    </div>
                  )}

                  <div className="application-footer">
                    <div className="applied-date">
                      <Calendar size={14} />
                      <span>Applied: {new Date(application.appliedAt).toLocaleDateString()}</span>
                    </div>

                    {application.status === 'pending' && (
                      <div className="application-actions">
                        <button
                          className="btn btn-success"
                          onClick={() => handleApplicationStatus(application.id, 'accepted')}
                        >
                          <Check size={16} />
                          Accept
                        </button>
                        <button
                          className="btn btn-danger"
                          onClick={() => handleApplicationStatus(application.id, 'rejected')}
                        >
                          <XCircle size={16} />
                          Reject
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ManageApplications;
