import { useState } from 'react';
import { <PERSON>, useNavigate } from 'react-router-dom';
import { Mail, Lock, Eye, EyeOff, Heart } from 'lucide-react';
import axios from 'axios';
import './Auth.css';

const EnhancedLogin = ({ onLogin }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [useFloatingLabels, setUseFloatingLabels] = useState(false);
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await axios.post('http://localhost:5000/api/auth/login', formData);
      
      if (response.data.success) {
        const user = response.data.data.user;
        
        // Check if user is admin
        if (user.role !== 'admin') {
          setError('Access denied. Admin privileges required.');
          setLoading(false);
          return;
        }
        
        onLogin(user, response.data.data.token);
        navigate('/admin');
      }
    } catch (error) {
      console.error('Admin login error:', error);
      setError(
        error.response?.data?.message || 
        'Login failed. Please check your credentials and try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  // Regular input field component
  const RegularInput = ({ type, name, placeholder, icon: Icon, required = false }) => (
    <div className="form-group">
      <label htmlFor={name} className="form-label">
        {placeholder}
      </label>
      <div className="input-wrapper">
        {Icon && <Icon className="input-icon" size={20} />}
        <input
          type={type}
          id={name}
          name={name}
          value={formData[name]}
          onChange={handleChange}
          className="form-input"
          placeholder={`Enter your ${placeholder.toLowerCase()}`}
          required={required}
        />
        {name === 'password' && (
          <button
            type="button"
            className="password-toggle"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        )}
      </div>
    </div>
  );

  // Floating label input component
  const FloatingLabelInput = ({ type, name, placeholder, icon: Icon, required = false }) => (
    <div className={`floating-label-wrapper ${!Icon ? 'no-icon' : ''}`}>
      <div className="input-wrapper">
        {Icon && <Icon className="input-icon" size={20} />}
        <input
          type={type}
          id={name}
          name={name}
          value={formData[name]}
          onChange={handleChange}
          className="form-input"
          placeholder=" " // Space to trigger :not(:placeholder-shown)
          required={required}
        />
        <label htmlFor={name} className="floating-label">
          {placeholder}
        </label>
        {name === 'password' && (
          <button
            type="button"
            className="password-toggle"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        )}
      </div>
    </div>
  );

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <div className="auth-logo">
            <Heart className="logo-icon admin-shield" />
            <span>Admin Portal</span>
          </div>
          <h2 className="auth-title">Administrator Login</h2>
          <p className="auth-subtitle">Access the admin dashboard</p>
          
          {/* Toggle for demo purposes */}
          <div className="demo-toggle">
            <label>
              <input
                type="checkbox"
                checked={useFloatingLabels}
                onChange={(e) => setUseFloatingLabels(e.target.checked)}
              />
              Use Floating Labels
            </label>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          {useFloatingLabels ? (
            <>
              <FloatingLabelInput
                type="email"
                name="email"
                placeholder="Admin Email"
                icon={Mail}
                required
              />
              
              <FloatingLabelInput
                type={showPassword ? 'text' : 'password'}
                name="password"
                placeholder="Password"
                icon={Lock}
                required
              />
            </>
          ) : (
            <>
              <RegularInput
                type="email"
                name="email"
                placeholder="Admin Email"
                icon={Mail}
                required
              />
              
              <RegularInput
                type={showPassword ? 'text' : 'password'}
                name="password"
                placeholder="Password"
                icon={Lock}
                required
              />
            </>
          )}

          <button
            type="submit"
            className="auth-button admin-button"
            disabled={loading}
          >
            {loading ? 'Signing In...' : 'Access Admin Dashboard'}
          </button>
        </form>

        <div className="auth-footer">
          <div className="admin-info">
            <p><strong>Default Admin Credentials:</strong></p>
            <p>Email: <EMAIL></p>
            <p>Password: admin123</p>
          </div>
          <p>
            <Link to="/" className="auth-link">
              ← Back to Home
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default EnhancedLogin;
