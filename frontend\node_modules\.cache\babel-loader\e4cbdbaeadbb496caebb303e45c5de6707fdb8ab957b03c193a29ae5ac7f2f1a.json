{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Companion\\\\frontend\\\\src\\\\components\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Mail, Lock, User, Phone, Eye, EyeOff, Heart } from 'lucide-react';\nimport axios from 'axios';\nimport './Auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Signup = ({\n  onLogin\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    password: '',\n    confirmPassword: '',\n    role: 'user'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n  const validateForm = () => {\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return false;\n    }\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return false;\n    }\n    return true;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    setLoading(true);\n    setError('');\n    try {\n      const {\n        confirmPassword,\n        ...submitData\n      } = formData;\n      const response = await axios.post('http://localhost:5000/api/auth/register', submitData);\n      if (response.data.success) {\n        onLogin(response.data.data.user, response.data.data.token);\n        navigate('/dashboard');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Signup error:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Registration failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-logo\",\n          children: [/*#__PURE__*/_jsxDEV(Heart, {\n            className: \"logo-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Companion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"auth-title\",\n          children: \"Join Our Community\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-subtitle\",\n          children: \"Create your account to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"auth-form\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"firstName\",\n              className: \"form-label\",\n              children: \"First Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(User, {\n                className: \"input-icon\",\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"firstName\",\n                name: \"firstName\",\n                value: formData.firstName,\n                onChange: handleChange,\n                className: \"form-input\",\n                placeholder: \"First name\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"lastName\",\n              className: \"form-label\",\n              children: \"Last Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(User, {\n                className: \"input-icon\",\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"lastName\",\n                name: \"lastName\",\n                value: formData.lastName,\n                onChange: handleChange,\n                className: \"form-input\",\n                placeholder: \"Last name\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            className: \"form-label\",\n            children: \"Email Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(Mail, {\n              className: \"input-icon\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              id: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleChange,\n              className: \"form-input\",\n              placeholder: \"Enter your email\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"phone\",\n            className: \"form-label\",\n            children: \"Phone Number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(Phone, {\n              className: \"input-icon\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              id: \"phone\",\n              name: \"phone\",\n              value: formData.phone,\n              onChange: handleChange,\n              className: \"form-input\",\n              placeholder: \"Enter your phone number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"role\",\n            className: \"form-label\",\n            children: \"I want to\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"role\",\n            name: \"role\",\n            value: formData.role,\n            onChange: handleChange,\n            className: \"form-select\",\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"user\",\n              children: \"Receive care services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"caregiver\",\n              children: \"Provide care services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"form-label\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(Lock, {\n                className: \"input-icon\",\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? 'text' : 'password',\n                id: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleChange,\n                className: \"form-input\",\n                placeholder: \"Create password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"password-toggle\",\n                onClick: () => setShowPassword(!showPassword),\n                children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 35\n                }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 58\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"confirmPassword\",\n              className: \"form-label\",\n              children: \"Confirm Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(Lock, {\n                className: \"input-icon\",\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: showConfirmPassword ? 'text' : 'password',\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                value: formData.confirmPassword,\n                onChange: handleChange,\n                className: \"form-input\",\n                placeholder: \"Confirm password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"password-toggle\",\n                onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 42\n                }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 65\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"auth-button\",\n          disabled: loading,\n          children: loading ? 'Creating Account...' : 'Create Account'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"auth-link\",\n            children: \"Sign in here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(Signup, \"uCcfU5hsKNeyELiQ/HYVEXmlros=\", false, function () {\n  return [useNavigate];\n});\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["useState", "Link", "useNavigate", "Mail", "Lock", "User", "Phone", "Eye", "Eye<PERSON>ff", "Heart", "axios", "jsxDEV", "_jsxDEV", "Signup", "onLogin", "_s", "formData", "setFormData", "firstName", "lastName", "email", "phone", "password", "confirmPassword", "role", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "loading", "setLoading", "error", "setError", "navigate", "handleChange", "e", "target", "name", "value", "validateForm", "length", "handleSubmit", "preventDefault", "submitData", "response", "post", "data", "success", "user", "token", "_error$response", "_error$response$data", "console", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "size", "type", "id", "onChange", "placeholder", "required", "onClick", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Companion/frontend/src/components/Signup.js"], "sourcesContent": ["import { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Mail, Lock, User, Phone, Eye, EyeOff, Heart } from 'lucide-react';\nimport axios from 'axios';\nimport './Auth.css';\n\nconst Signup = ({ onLogin }) => {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    password: '',\n    confirmPassword: '',\n    role: 'user'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n\n  const validateForm = () => {\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return false;\n    }\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return false;\n    }\n    return true;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n    \n    setLoading(true);\n    setError('');\n\n    try {\n      const { confirmPassword, ...submitData } = formData;\n      const response = await axios.post('http://localhost:5000/api/auth/register', submitData);\n      \n      if (response.data.success) {\n        onLogin(response.data.data.user, response.data.data.token);\n        navigate('/dashboard');\n      }\n    } catch (error) {\n      console.error('Signup error:', error);\n      setError(\n        error.response?.data?.message || \n        'Registration failed. Please try again.'\n      );\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <div className=\"auth-logo\">\n            <Heart className=\"logo-icon\" />\n            <span>Companion</span>\n          </div>\n          <h2 className=\"auth-title\">Join Our Community</h2>\n          <p className=\"auth-subtitle\">Create your account to get started</p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"auth-form\">\n          {error && (\n            <div className=\"error-message\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"firstName\" className=\"form-label\">\n                First Name\n              </label>\n              <div className=\"input-wrapper\">\n                <User className=\"input-icon\" size={20} />\n                <input\n                  type=\"text\"\n                  id=\"firstName\"\n                  name=\"firstName\"\n                  value={formData.firstName}\n                  onChange={handleChange}\n                  className=\"form-input\"\n                  placeholder=\"First name\"\n                  required\n                />\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"lastName\" className=\"form-label\">\n                Last Name\n              </label>\n              <div className=\"input-wrapper\">\n                <User className=\"input-icon\" size={20} />\n                <input\n                  type=\"text\"\n                  id=\"lastName\"\n                  name=\"lastName\"\n                  value={formData.lastName}\n                  onChange={handleChange}\n                  className=\"form-input\"\n                  placeholder=\"Last name\"\n                  required\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"email\" className=\"form-label\">\n              Email Address\n            </label>\n            <div className=\"input-wrapper\">\n              <Mail className=\"input-icon\" size={20} />\n              <input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleChange}\n                className=\"form-input\"\n                placeholder=\"Enter your email\"\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"phone\" className=\"form-label\">\n              Phone Number\n            </label>\n            <div className=\"input-wrapper\">\n              <Phone className=\"input-icon\" size={20} />\n              <input\n                type=\"tel\"\n                id=\"phone\"\n                name=\"phone\"\n                value={formData.phone}\n                onChange={handleChange}\n                className=\"form-input\"\n                placeholder=\"Enter your phone number\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"role\" className=\"form-label\">\n              I want to\n            </label>\n            <select\n              id=\"role\"\n              name=\"role\"\n              value={formData.role}\n              onChange={handleChange}\n              className=\"form-select\"\n              required\n            >\n              <option value=\"user\">Receive care services</option>\n              <option value=\"caregiver\">Provide care services</option>\n            </select>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"password\" className=\"form-label\">\n                Password\n              </label>\n              <div className=\"input-wrapper\">\n                <Lock className=\"input-icon\" size={20} />\n                <input\n                  type={showPassword ? 'text' : 'password'}\n                  id=\"password\"\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                  className=\"form-input\"\n                  placeholder=\"Create password\"\n                  required\n                />\n                <button\n                  type=\"button\"\n                  className=\"password-toggle\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}\n                </button>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"confirmPassword\" className=\"form-label\">\n                Confirm Password\n              </label>\n              <div className=\"input-wrapper\">\n                <Lock className=\"input-icon\" size={20} />\n                <input\n                  type={showConfirmPassword ? 'text' : 'password'}\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  value={formData.confirmPassword}\n                  onChange={handleChange}\n                  className=\"form-input\"\n                  placeholder=\"Confirm password\"\n                  required\n                />\n                <button\n                  type=\"button\"\n                  className=\"password-toggle\"\n                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                >\n                  {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <button\n            type=\"submit\"\n            className=\"auth-button\"\n            disabled={loading}\n          >\n            {loading ? 'Creating Account...' : 'Create Account'}\n          </button>\n        </form>\n\n        <div className=\"auth-footer\">\n          <p>\n            Already have an account?{' '}\n            <Link to=\"/login\" className=\"auth-link\">\n              Sign in here\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Signup;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,QAAQ,cAAc;AAC1E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC9B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMiC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAE9B,MAAMgC,YAAY,GAAIC,CAAC,IAAK;IAC1BlB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACmB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;IACF;IACA,IAAIP,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIvB,QAAQ,CAACM,QAAQ,KAAKN,QAAQ,CAACO,eAAe,EAAE;MAClDS,QAAQ,CAAC,wBAAwB,CAAC;MAClC,OAAO,KAAK;IACd;IACA,IAAIhB,QAAQ,CAACM,QAAQ,CAACkB,MAAM,GAAG,CAAC,EAAE;MAChCR,QAAQ,CAAC,6CAA6C,CAAC;MACvD,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMS,YAAY,GAAG,MAAON,CAAC,IAAK;IAChCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAElB,IAAI,CAACH,YAAY,CAAC,CAAC,EAAE;IAErBT,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAM;QAAET,eAAe;QAAE,GAAGoB;MAAW,CAAC,GAAG3B,QAAQ;MACnD,MAAM4B,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,IAAI,CAAC,yCAAyC,EAAEF,UAAU,CAAC;MAExF,IAAIC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBjC,OAAO,CAAC8B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACE,IAAI,EAAEJ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACG,KAAK,CAAC;QAC1DhB,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACrB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCC,QAAQ,CACN,EAAAkB,eAAA,GAAAnB,KAAK,CAACa,QAAQ,cAAAM,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBJ,IAAI,cAAAK,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAC7B,wCACF,CAAC;IACH,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACElB,OAAA;IAAK0C,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7B3C,OAAA;MAAK0C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB3C,OAAA;QAAK0C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3C,OAAA,CAACH,KAAK;YAAC6C,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B/C,OAAA;YAAA2C,QAAA,EAAM;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACN/C,OAAA;UAAI0C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClD/C,OAAA;UAAG0C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eAEN/C,OAAA;QAAMgD,QAAQ,EAAEnB,YAAa;QAACa,SAAS,EAAC,WAAW;QAAAC,QAAA,GAChDxB,KAAK,iBACJnB,OAAA;UAAK0C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BxB;QAAK;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED/C,OAAA;UAAK0C,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB3C,OAAA;YAAK0C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3C,OAAA;cAAOiD,OAAO,EAAC,WAAW;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/C,OAAA;cAAK0C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3C,OAAA,CAACP,IAAI;gBAACiD,SAAS,EAAC,YAAY;gBAACQ,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC/C,OAAA;gBACEmD,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,WAAW;gBACd3B,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAEtB,QAAQ,CAACE,SAAU;gBAC1B+C,QAAQ,EAAE/B,YAAa;gBACvBoB,SAAS,EAAC,YAAY;gBACtBY,WAAW,EAAC,YAAY;gBACxBC,QAAQ;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/C,OAAA;YAAK0C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3C,OAAA;cAAOiD,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/C,OAAA;cAAK0C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3C,OAAA,CAACP,IAAI;gBAACiD,SAAS,EAAC,YAAY;gBAACQ,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC/C,OAAA;gBACEmD,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,UAAU;gBACb3B,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEtB,QAAQ,CAACG,QAAS;gBACzB8C,QAAQ,EAAE/B,YAAa;gBACvBoB,SAAS,EAAC,YAAY;gBACtBY,WAAW,EAAC,WAAW;gBACvBC,QAAQ;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA;UAAK0C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3C,OAAA;YAAOiD,OAAO,EAAC,OAAO;YAACP,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/C,OAAA;YAAK0C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B3C,OAAA,CAACT,IAAI;cAACmD,SAAS,EAAC,YAAY;cAACQ,IAAI,EAAE;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzC/C,OAAA;cACEmD,IAAI,EAAC,OAAO;cACZC,EAAE,EAAC,OAAO;cACV3B,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEtB,QAAQ,CAACI,KAAM;cACtB6C,QAAQ,EAAE/B,YAAa;cACvBoB,SAAS,EAAC,YAAY;cACtBY,WAAW,EAAC,kBAAkB;cAC9BC,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA;UAAK0C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3C,OAAA;YAAOiD,OAAO,EAAC,OAAO;YAACP,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/C,OAAA;YAAK0C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B3C,OAAA,CAACN,KAAK;cAACgD,SAAS,EAAC,YAAY;cAACQ,IAAI,EAAE;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1C/C,OAAA;cACEmD,IAAI,EAAC,KAAK;cACVC,EAAE,EAAC,OAAO;cACV3B,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEtB,QAAQ,CAACK,KAAM;cACtB4C,QAAQ,EAAE/B,YAAa;cACvBoB,SAAS,EAAC,YAAY;cACtBY,WAAW,EAAC;YAAyB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA;UAAK0C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3C,OAAA;YAAOiD,OAAO,EAAC,MAAM;YAACP,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE7C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/C,OAAA;YACEoD,EAAE,EAAC,MAAM;YACT3B,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEtB,QAAQ,CAACQ,IAAK;YACrByC,QAAQ,EAAE/B,YAAa;YACvBoB,SAAS,EAAC,aAAa;YACvBa,QAAQ;YAAAZ,QAAA,gBAER3C,OAAA;cAAQ0B,KAAK,EAAC,MAAM;cAAAiB,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnD/C,OAAA;cAAQ0B,KAAK,EAAC,WAAW;cAAAiB,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN/C,OAAA;UAAK0C,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB3C,OAAA;YAAK0C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3C,OAAA;cAAOiD,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/C,OAAA;cAAK0C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3C,OAAA,CAACR,IAAI;gBAACkD,SAAS,EAAC,YAAY;gBAACQ,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC/C,OAAA;gBACEmD,IAAI,EAAEtC,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCuC,EAAE,EAAC,UAAU;gBACb3B,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEtB,QAAQ,CAACM,QAAS;gBACzB2C,QAAQ,EAAE/B,YAAa;gBACvBoB,SAAS,EAAC,YAAY;gBACtBY,WAAW,EAAC,iBAAiB;gBAC7BC,QAAQ;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACF/C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbT,SAAS,EAAC,iBAAiB;gBAC3Bc,OAAO,EAAEA,CAAA,KAAM1C,eAAe,CAAC,CAACD,YAAY,CAAE;gBAAA8B,QAAA,EAE7C9B,YAAY,gBAAGb,OAAA,CAACJ,MAAM;kBAACsD,IAAI,EAAE;gBAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG/C,OAAA,CAACL,GAAG;kBAACuD,IAAI,EAAE;gBAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/C,OAAA;YAAK0C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3C,OAAA;cAAOiD,OAAO,EAAC,iBAAiB;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/C,OAAA;cAAK0C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3C,OAAA,CAACR,IAAI;gBAACkD,SAAS,EAAC,YAAY;gBAACQ,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC/C,OAAA;gBACEmD,IAAI,EAAEpC,mBAAmB,GAAG,MAAM,GAAG,UAAW;gBAChDqC,EAAE,EAAC,iBAAiB;gBACpB3B,IAAI,EAAC,iBAAiB;gBACtBC,KAAK,EAAEtB,QAAQ,CAACO,eAAgB;gBAChC0C,QAAQ,EAAE/B,YAAa;gBACvBoB,SAAS,EAAC,YAAY;gBACtBY,WAAW,EAAC,kBAAkB;gBAC9BC,QAAQ;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACF/C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbT,SAAS,EAAC,iBAAiB;gBAC3Bc,OAAO,EAAEA,CAAA,KAAMxC,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;gBAAA4B,QAAA,EAE3D5B,mBAAmB,gBAAGf,OAAA,CAACJ,MAAM;kBAACsD,IAAI,EAAE;gBAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG/C,OAAA,CAACL,GAAG;kBAACuD,IAAI,EAAE;gBAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA;UACEmD,IAAI,EAAC,QAAQ;UACbT,SAAS,EAAC,aAAa;UACvBe,QAAQ,EAAExC,OAAQ;UAAA0B,QAAA,EAEjB1B,OAAO,GAAG,qBAAqB,GAAG;QAAgB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP/C,OAAA;QAAK0C,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B3C,OAAA;UAAA2C,QAAA,GAAG,0BACuB,EAAC,GAAG,eAC5B3C,OAAA,CAACX,IAAI;YAACqE,EAAE,EAAC,QAAQ;YAAChB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAExC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA3PIF,MAAM;EAAA,QAcOX,WAAW;AAAA;AAAAqE,EAAA,GAdxB1D,MAAM;AA6PZ,eAAeA,MAAM;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}