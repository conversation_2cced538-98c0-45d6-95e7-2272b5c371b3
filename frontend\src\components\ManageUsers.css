.manage-users {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.manage-users-header {
  margin-bottom: 30px;
}

.manage-users-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
}

.manage-users-header p {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

/* Filters */
.filters-section {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-box svg {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.search-box input {
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
}

.filter-select {
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  min-width: 150px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
}

/* Users Table */
.users-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  background: #f8f9fa;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e9ecef;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.users-table td {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.user-email {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 0.9rem;
}

.role-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.role-badge.user {
  background: #e3f2fd;
  color: #1976d2;
}

.role-badge.caregiver {
  background: #fce4ec;
  color: #c2185b;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.pending {
  background: #fff3e0;
  color: #f57c00;
}

.status-badge.active {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-badge.blocked {
  background: #ffebee;
  color: #d32f2f;
}

.join-date {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 0.9rem;
}

.user-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn.approve {
  background: #e8f5e8;
  color: #2e7d32;
}

.action-btn.approve:hover {
  background: #c8e6c9;
  transform: scale(1.1);
}

.action-btn.unapprove {
  background: #fff3e0;
  color: #f57c00;
}

.action-btn.unapprove:hover {
  background: #ffe0b2;
  transform: scale(1.1);
}

.action-btn.block {
  background: #fff3e0;
  color: #f57c00;
}

.action-btn.block:hover {
  background: #ffe0b2;
  transform: scale(1.1);
}

.action-btn.unblock {
  background: #e8f5e8;
  color: #2e7d32;
}

.action-btn.unblock:hover {
  background: #c8e6c9;
  transform: scale(1.1);
}

.action-btn.delete {
  background: #ffebee;
  color: #d32f2f;
}

.action-btn.delete:hover {
  background: #ffcdd2;
  transform: scale(1.1);
}

/* Loading and Empty States */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.no-data {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin: 20px 0;
}

.pagination-btn {
  padding: 10px 20px;
  border: 2px solid #667eea;
  background: white;
  color: #667eea;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #667eea;
  color: white;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #666;
  font-size: 0.9rem;
}

/* Info Box */
.info-box {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.info-box svg {
  color: #1976d2;
  flex-shrink: 0;
  margin-top: 2px;
}

.info-box strong {
  color: #1565c0;
}

.info-box ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.info-box li {
  margin-bottom: 5px;
  color: #1565c0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .manage-users {
    padding: 15px;
  }
  
  .filters-section {
    flex-direction: column;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .users-table-container {
    overflow-x: auto;
  }
  
  .users-table {
    min-width: 700px;
  }
  
  .user-actions {
    flex-direction: column;
    gap: 4px;
  }
  
  .pagination {
    flex-direction: column;
    gap: 10px;
  }
  
  .info-box {
    flex-direction: column;
    gap: 10px;
  }
}
