{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Companion\\\\frontend\\\\src\\\\components\\\\ManageUsers.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { Search, Filter, UserCheck, UserX, Trash2, Shield, User, Mail, Calendar, AlertTriangle } from 'lucide-react';\nimport axios from 'axios';\nimport './ManageUsers.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ManageUsers = () => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [roleFilter, setRoleFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 20,\n    total: 0,\n    pages: 0\n  });\n  useEffect(() => {\n    fetchUsers();\n  }, [pagination.page, roleFilter, searchTerm, statusFilter]);\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const token = localStorage.getItem('token');\n      const params = new URLSearchParams({\n        page: pagination.page,\n        limit: pagination.limit,\n        role: roleFilter,\n        search: searchTerm\n      });\n      const response = await axios.get(`http://localhost:5000/api/admin/users?${params}`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setUsers(response.data.data.users);\n        setPagination(prev => ({\n          ...prev,\n          ...response.data.data.pagination\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleUserAction = async (userId, action, value = true) => {\n    try {\n      const token = localStorage.getItem('token');\n      if (action === 'delete') {\n        if (!window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {\n          return;\n        }\n        await axios.delete(`http://localhost:5000/api/admin/users/${userId}`, {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        });\n        alert('User deleted successfully');\n      } else {\n        const updateData = {};\n        if (action === 'verify') updateData.isVerified = value;\n        if (action === 'block') updateData.isBlocked = value;\n        await axios.patch(`http://localhost:5000/api/admin/users/${userId}/status`, updateData, {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        });\n        const actionText = action === 'verify' ? value ? 'approved' : 'unapproved' : value ? 'blocked' : 'unblocked';\n        alert(`User ${actionText} successfully`);\n      }\n      fetchUsers(); // Refresh the list\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(`Error performing ${action}:`, error);\n      alert(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || `Failed to ${action} user`);\n    }\n  };\n  const getStatusBadge = user => {\n    if (user.isBlocked) return {\n      text: 'Blocked',\n      class: 'blocked'\n    };\n    if (user.isVerified) return {\n      text: 'Active',\n      class: 'active'\n    };\n    return {\n      text: 'Pending',\n      class: 'pending'\n    };\n  };\n  const filteredUsers = users.filter(user => {\n    if (statusFilter !== 'all') {\n      const status = getStatusBadge(user);\n      if (statusFilter !== status.text.toLowerCase()) return false;\n    }\n    return true;\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"manage-users\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"manage-users-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Manage Users\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Comprehensive user management with approval, blocking, and deletion capabilities\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filters-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-box\",\n        children: [/*#__PURE__*/_jsxDEV(Search, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search users by name or email...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: roleFilter,\n        onChange: e => setRoleFilter(e.target.value),\n        className: \"filter-select\",\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"all\",\n          children: \"All Roles\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"user\",\n          children: \"Users\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"caregiver\",\n          children: \"Caregivers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: statusFilter,\n        onChange: e => setStatusFilter(e.target.value),\n        className: \"filter-select\",\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"all\",\n          children: \"All Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"pending\",\n          children: \"Pending\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"active\",\n          children: \"Active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"blocked\",\n          children: \"Blocked\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"users-table-container\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading users...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"users-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"User\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Joined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredUsers.length > 0 ? filteredUsers.map(user => {\n            const status = getStatusBadge(user);\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"user-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-avatar\",\n                  children: /*#__PURE__*/_jsxDEV(User, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"user-name\",\n                    children: user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"user-email\",\n                    children: [/*#__PURE__*/_jsxDEV(Mail, {\n                      size: 14\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 29\n                    }, this), user.email]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `role-badge ${user.role}`,\n                  children: user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `status-badge ${status.class}`,\n                  children: status.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"join-date\",\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 25\n                }, this), new Date(user.createdAt).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"user-actions\",\n                children: [!user.isBlocked && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [!user.isVerified ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn approve\",\n                    onClick: () => handleUserAction(user.id, 'verify', true),\n                    title: \"Approve User\",\n                    children: /*#__PURE__*/_jsxDEV(UserCheck, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 31\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn unapprove\",\n                    onClick: () => handleUserAction(user.id, 'verify', false),\n                    title: \"Unapprove User\",\n                    children: /*#__PURE__*/_jsxDEV(UserX, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn block\",\n                    onClick: () => handleUserAction(user.id, 'block', true),\n                    title: \"Block User\",\n                    children: /*#__PURE__*/_jsxDEV(Shield, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true), user.isBlocked && /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"action-btn unblock\",\n                  onClick: () => handleUserAction(user.id, 'block', false),\n                  title: \"Unblock User\",\n                  children: /*#__PURE__*/_jsxDEV(UserCheck, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"action-btn delete\",\n                  onClick: () => handleUserAction(user.id, 'delete'),\n                  title: \"Delete User\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 23\n              }, this)]\n            }, user.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 21\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: \"5\",\n              className: \"no-data\",\n              children: \"No users found matching your criteria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), pagination.pages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pagination\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"pagination-btn\",\n        disabled: pagination.page === 1,\n        onClick: () => setPagination(prev => ({\n          ...prev,\n          page: prev.page - 1\n        })),\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"pagination-info\",\n        children: [\"Page \", pagination.page, \" of \", pagination.pages, \" (\", pagination.total, \" users)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"pagination-btn\",\n        disabled: pagination.page === pagination.pages,\n        onClick: () => setPagination(prev => ({\n          ...prev,\n          page: prev.page + 1\n        })),\n        children: \"Next\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"info-box\",\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"User Management Guide:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Approve:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this), \" Allow pending users to login and access the platform\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Block:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this), \" Prevent users from logging in (reversible)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Delete:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), \" Permanently remove user and all their data (irreversible)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(ManageUsers, \"GK2A+FlLiqc35ODus3xsSCNMbFw=\");\n_c = ManageUsers;\nexport default ManageUsers;\nvar _c;\n$RefreshReg$(_c, \"ManageUsers\");", "map": {"version": 3, "names": ["useState", "useEffect", "Search", "Filter", "UserCheck", "UserX", "Trash2", "Shield", "User", "Mail", "Calendar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ManageUsers", "_s", "users", "setUsers", "loading", "setLoading", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON>er", "setRoleFilter", "statusFilter", "setStatus<PERSON>ilter", "pagination", "setPagination", "page", "limit", "total", "pages", "fetchUsers", "token", "localStorage", "getItem", "params", "URLSearchParams", "role", "search", "response", "get", "headers", "Authorization", "data", "success", "prev", "error", "console", "handleUserAction", "userId", "action", "value", "window", "confirm", "delete", "alert", "updateData", "isVerified", "isBlocked", "patch", "actionText", "_error$response", "_error$response$data", "message", "getStatusBadge", "user", "text", "class", "filteredUsers", "filter", "status", "toLowerCase", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "type", "placeholder", "onChange", "e", "target", "length", "map", "name", "email", "Date", "createdAt", "toLocaleDateString", "onClick", "id", "title", "colSpan", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Companion/frontend/src/components/ManageUsers.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { \n  Search, \n  Filter, \n  UserCheck, \n  UserX, \n  Trash2, \n  Shield, \n  User,\n  Mail,\n  Calendar,\n  AlertTriangle\n} from 'lucide-react';\nimport axios from 'axios';\nimport './ManageUsers.css';\n\nconst ManageUsers = () => {\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [roleFilter, setRoleFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 20,\n    total: 0,\n    pages: 0\n  });\n\n  useEffect(() => {\n    fetchUsers();\n  }, [pagination.page, roleFilter, searchTerm, statusFilter]);\n\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const token = localStorage.getItem('token');\n      const params = new URLSearchParams({\n        page: pagination.page,\n        limit: pagination.limit,\n        role: roleFilter,\n        search: searchTerm\n      });\n\n      const response = await axios.get(`http://localhost:5000/api/admin/users?${params}`, {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setUsers(response.data.data.users);\n        setPagination(prev => ({\n          ...prev,\n          ...response.data.data.pagination\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleUserAction = async (userId, action, value = true) => {\n    try {\n      const token = localStorage.getItem('token');\n      \n      if (action === 'delete') {\n        if (!window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {\n          return;\n        }\n        \n        await axios.delete(`http://localhost:5000/api/admin/users/${userId}`, {\n          headers: { Authorization: `Bearer ${token}` }\n        });\n        \n        alert('User deleted successfully');\n      } else {\n        const updateData = {};\n        if (action === 'verify') updateData.isVerified = value;\n        if (action === 'block') updateData.isBlocked = value;\n        \n        await axios.patch(`http://localhost:5000/api/admin/users/${userId}/status`, \n          updateData,\n          { headers: { Authorization: `Bearer ${token}` } }\n        );\n        \n        const actionText = action === 'verify' \n          ? (value ? 'approved' : 'unapproved') \n          : (value ? 'blocked' : 'unblocked');\n        alert(`User ${actionText} successfully`);\n      }\n      \n      fetchUsers(); // Refresh the list\n    } catch (error) {\n      console.error(`Error performing ${action}:`, error);\n      alert(error.response?.data?.message || `Failed to ${action} user`);\n    }\n  };\n\n  const getStatusBadge = (user) => {\n    if (user.isBlocked) return { text: 'Blocked', class: 'blocked' };\n    if (user.isVerified) return { text: 'Active', class: 'active' };\n    return { text: 'Pending', class: 'pending' };\n  };\n\n  const filteredUsers = users.filter(user => {\n    if (statusFilter !== 'all') {\n      const status = getStatusBadge(user);\n      if (statusFilter !== status.text.toLowerCase()) return false;\n    }\n    return true;\n  });\n\n  return (\n    <div className=\"manage-users\">\n      <div className=\"manage-users-header\">\n        <h2>Manage Users</h2>\n        <p>Comprehensive user management with approval, blocking, and deletion capabilities</p>\n      </div>\n\n      {/* Filters */}\n      <div className=\"filters-section\">\n        <div className=\"search-box\">\n          <Search size={20} />\n          <input\n            type=\"text\"\n            placeholder=\"Search users by name or email...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </div>\n\n        <select\n          value={roleFilter}\n          onChange={(e) => setRoleFilter(e.target.value)}\n          className=\"filter-select\"\n        >\n          <option value=\"all\">All Roles</option>\n          <option value=\"user\">Users</option>\n          <option value=\"caregiver\">Caregivers</option>\n        </select>\n\n        <select\n          value={statusFilter}\n          onChange={(e) => setStatusFilter(e.target.value)}\n          className=\"filter-select\"\n        >\n          <option value=\"all\">All Status</option>\n          <option value=\"pending\">Pending</option>\n          <option value=\"active\">Active</option>\n          <option value=\"blocked\">Blocked</option>\n        </select>\n      </div>\n\n      {/* Users Table */}\n      <div className=\"users-table-container\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading users...</p>\n          </div>\n        ) : (\n          <table className=\"users-table\">\n            <thead>\n              <tr>\n                <th>User</th>\n                <th>Role</th>\n                <th>Status</th>\n                <th>Joined</th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredUsers.length > 0 ? (\n                filteredUsers.map(user => {\n                  const status = getStatusBadge(user);\n                  return (\n                    <tr key={user.id}>\n                      <td className=\"user-info\">\n                        <div className=\"user-avatar\">\n                          <User size={20} />\n                        </div>\n                        <div className=\"user-details\">\n                          <div className=\"user-name\">{user.name}</div>\n                          <div className=\"user-email\">\n                            <Mail size={14} />\n                            {user.email}\n                          </div>\n                        </div>\n                      </td>\n                      \n                      <td>\n                        <span className={`role-badge ${user.role}`}>\n                          {user.role}\n                        </span>\n                      </td>\n                      \n                      <td>\n                        <span className={`status-badge ${status.class}`}>\n                          {status.text}\n                        </span>\n                      </td>\n                      \n                      <td className=\"join-date\">\n                        <Calendar size={14} />\n                        {new Date(user.createdAt).toLocaleDateString()}\n                      </td>\n                      \n                      <td className=\"user-actions\">\n                        {!user.isBlocked && (\n                          <>\n                            {!user.isVerified ? (\n                              <button\n                                className=\"action-btn approve\"\n                                onClick={() => handleUserAction(user.id, 'verify', true)}\n                                title=\"Approve User\"\n                              >\n                                <UserCheck size={16} />\n                              </button>\n                            ) : (\n                              <button\n                                className=\"action-btn unapprove\"\n                                onClick={() => handleUserAction(user.id, 'verify', false)}\n                                title=\"Unapprove User\"\n                              >\n                                <UserX size={16} />\n                              </button>\n                            )}\n                            \n                            <button\n                              className=\"action-btn block\"\n                              onClick={() => handleUserAction(user.id, 'block', true)}\n                              title=\"Block User\"\n                            >\n                              <Shield size={16} />\n                            </button>\n                          </>\n                        )}\n                        \n                        {user.isBlocked && (\n                          <button\n                            className=\"action-btn unblock\"\n                            onClick={() => handleUserAction(user.id, 'block', false)}\n                            title=\"Unblock User\"\n                          >\n                            <UserCheck size={16} />\n                          </button>\n                        )}\n                        \n                        <button\n                          className=\"action-btn delete\"\n                          onClick={() => handleUserAction(user.id, 'delete')}\n                          title=\"Delete User\"\n                        >\n                          <Trash2 size={16} />\n                        </button>\n                      </td>\n                    </tr>\n                  );\n                })\n              ) : (\n                <tr>\n                  <td colSpan=\"5\" className=\"no-data\">\n                    No users found matching your criteria\n                  </td>\n                </tr>\n              )}\n            </tbody>\n          </table>\n        )}\n      </div>\n\n      {/* Pagination */}\n      {pagination.pages > 1 && (\n        <div className=\"pagination\">\n          <button\n            className=\"pagination-btn\"\n            disabled={pagination.page === 1}\n            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}\n          >\n            Previous\n          </button>\n          \n          <span className=\"pagination-info\">\n            Page {pagination.page} of {pagination.pages} ({pagination.total} users)\n          </span>\n          \n          <button\n            className=\"pagination-btn\"\n            disabled={pagination.page === pagination.pages}\n            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}\n          >\n            Next\n          </button>\n        </div>\n      )}\n\n      {/* Info Box */}\n      <div className=\"info-box\">\n        <AlertTriangle size={20} />\n        <div>\n          <strong>User Management Guide:</strong>\n          <ul>\n            <li><strong>Approve:</strong> Allow pending users to login and access the platform</li>\n            <li><strong>Block:</strong> Prevent users from logging in (reversible)</li>\n            <li><strong>Delete:</strong> Permanently remove user and all their data (irreversible)</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ManageUsers;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SACEC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,aAAa,QACR,cAAc;AACrB,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC;IAC3C+B,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFjC,SAAS,CAAC,MAAM;IACdkC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACN,UAAU,CAACE,IAAI,EAAEN,UAAU,EAAEF,UAAU,EAAEI,YAAY,CAAC,CAAC;EAE3D,MAAMQ,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMc,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCT,IAAI,EAAEF,UAAU,CAACE,IAAI;QACrBC,KAAK,EAAEH,UAAU,CAACG,KAAK;QACvBS,IAAI,EAAEhB,UAAU;QAChBiB,MAAM,EAAEnB;MACV,CAAC,CAAC;MAEF,MAAMoB,QAAQ,GAAG,MAAM/B,KAAK,CAACgC,GAAG,CAAC,yCAAyCL,MAAM,EAAE,EAAE;QAClFM,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUV,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIO,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzB5B,QAAQ,CAACuB,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC5B,KAAK,CAAC;QAClCW,aAAa,CAACmB,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP,GAAGN,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAClB;QACxB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACR5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,gBAAgB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,MAAM,EAAEC,KAAK,GAAG,IAAI,KAAK;IAC/D,IAAI;MACF,MAAMnB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAIgB,MAAM,KAAK,QAAQ,EAAE;QACvB,IAAI,CAACE,MAAM,CAACC,OAAO,CAAC,0EAA0E,CAAC,EAAE;UAC/F;QACF;QAEA,MAAM7C,KAAK,CAAC8C,MAAM,CAAC,yCAAyCL,MAAM,EAAE,EAAE;UACpER,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUV,KAAK;UAAG;QAC9C,CAAC,CAAC;QAEFuB,KAAK,CAAC,2BAA2B,CAAC;MACpC,CAAC,MAAM;QACL,MAAMC,UAAU,GAAG,CAAC,CAAC;QACrB,IAAIN,MAAM,KAAK,QAAQ,EAAEM,UAAU,CAACC,UAAU,GAAGN,KAAK;QACtD,IAAID,MAAM,KAAK,OAAO,EAAEM,UAAU,CAACE,SAAS,GAAGP,KAAK;QAEpD,MAAM3C,KAAK,CAACmD,KAAK,CAAC,yCAAyCV,MAAM,SAAS,EACxEO,UAAU,EACV;UAAEf,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUV,KAAK;UAAG;QAAE,CAClD,CAAC;QAED,MAAM4B,UAAU,GAAGV,MAAM,KAAK,QAAQ,GACjCC,KAAK,GAAG,UAAU,GAAG,YAAY,GACjCA,KAAK,GAAG,SAAS,GAAG,WAAY;QACrCI,KAAK,CAAC,QAAQK,UAAU,eAAe,CAAC;MAC1C;MAEA7B,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOe,KAAK,EAAE;MAAA,IAAAe,eAAA,EAAAC,oBAAA;MACdf,OAAO,CAACD,KAAK,CAAC,oBAAoBI,MAAM,GAAG,EAAEJ,KAAK,CAAC;MACnDS,KAAK,CAAC,EAAAM,eAAA,GAAAf,KAAK,CAACP,QAAQ,cAAAsB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBlB,IAAI,cAAAmB,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,aAAab,MAAM,OAAO,CAAC;IACpE;EACF,CAAC;EAED,MAAMc,cAAc,GAAIC,IAAI,IAAK;IAC/B,IAAIA,IAAI,CAACP,SAAS,EAAE,OAAO;MAAEQ,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC;IAChE,IAAIF,IAAI,CAACR,UAAU,EAAE,OAAO;MAAES,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC;IAC/D,OAAO;MAAED,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC;EAC9C,CAAC;EAED,MAAMC,aAAa,GAAGrD,KAAK,CAACsD,MAAM,CAACJ,IAAI,IAAI;IACzC,IAAI1C,YAAY,KAAK,KAAK,EAAE;MAC1B,MAAM+C,MAAM,GAAGN,cAAc,CAACC,IAAI,CAAC;MACnC,IAAI1C,YAAY,KAAK+C,MAAM,CAACJ,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE,OAAO,KAAK;IAC9D;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EAEF,oBACE7D,OAAA;IAAK8D,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B/D,OAAA;MAAK8D,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC/D,OAAA;QAAA+D,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBnE,OAAA;QAAA+D,QAAA,EAAG;MAAgF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC,eAGNnE,OAAA;MAAK8D,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B/D,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/D,OAAA,CAACZ,MAAM;UAACgF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpBnE,OAAA;UACEqE,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,kCAAkC;UAC9C7B,KAAK,EAAEhC,UAAW;UAClB8D,QAAQ,EAAGC,CAAC,IAAK9D,aAAa,CAAC8D,CAAC,CAACC,MAAM,CAAChC,KAAK;QAAE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnE,OAAA;QACEyC,KAAK,EAAE9B,UAAW;QAClB4D,QAAQ,EAAGC,CAAC,IAAK5D,aAAa,CAAC4D,CAAC,CAACC,MAAM,CAAChC,KAAK,CAAE;QAC/CqB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAEzB/D,OAAA;UAAQyC,KAAK,EAAC,KAAK;UAAAsB,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtCnE,OAAA;UAAQyC,KAAK,EAAC,MAAM;UAAAsB,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnCnE,OAAA;UAAQyC,KAAK,EAAC,WAAW;UAAAsB,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAETnE,OAAA;QACEyC,KAAK,EAAE5B,YAAa;QACpB0D,QAAQ,EAAGC,CAAC,IAAK1D,eAAe,CAAC0D,CAAC,CAACC,MAAM,CAAChC,KAAK,CAAE;QACjDqB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAEzB/D,OAAA;UAAQyC,KAAK,EAAC,KAAK;UAAAsB,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvCnE,OAAA;UAAQyC,KAAK,EAAC,SAAS;UAAAsB,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxCnE,OAAA;UAAQyC,KAAK,EAAC,QAAQ;UAAAsB,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtCnE,OAAA;UAAQyC,KAAK,EAAC,SAAS;UAAAsB,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnE,OAAA;MAAK8D,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EACnCxD,OAAO,gBACNP,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B/D,OAAA;UAAK8D,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCnE,OAAA;UAAA+D,QAAA,EAAG;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,gBAENnE,OAAA;QAAO8D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC5B/D,OAAA;UAAA+D,QAAA,eACE/D,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAA+D,QAAA,EAAI;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACbnE,OAAA;cAAA+D,QAAA,EAAI;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACbnE,OAAA;cAAA+D,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACfnE,OAAA;cAAA+D,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACfnE,OAAA;cAAA+D,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRnE,OAAA;UAAA+D,QAAA,EACGL,aAAa,CAACgB,MAAM,GAAG,CAAC,GACvBhB,aAAa,CAACiB,GAAG,CAACpB,IAAI,IAAI;YACxB,MAAMK,MAAM,GAAGN,cAAc,CAACC,IAAI,CAAC;YACnC,oBACEvD,OAAA;cAAA+D,QAAA,gBACE/D,OAAA;gBAAI8D,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACvB/D,OAAA;kBAAK8D,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1B/D,OAAA,CAACN,IAAI;oBAAC0E,IAAI,EAAE;kBAAG;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACNnE,OAAA;kBAAK8D,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B/D,OAAA;oBAAK8D,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAER,IAAI,CAACqB;kBAAI;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5CnE,OAAA;oBAAK8D,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzB/D,OAAA,CAACL,IAAI;sBAACyE,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACjBZ,IAAI,CAACsB,KAAK;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAELnE,OAAA;gBAAA+D,QAAA,eACE/D,OAAA;kBAAM8D,SAAS,EAAE,cAAcP,IAAI,CAAC5B,IAAI,EAAG;kBAAAoC,QAAA,EACxCR,IAAI,CAAC5B;gBAAI;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAELnE,OAAA;gBAAA+D,QAAA,eACE/D,OAAA;kBAAM8D,SAAS,EAAE,gBAAgBF,MAAM,CAACH,KAAK,EAAG;kBAAAM,QAAA,EAC7CH,MAAM,CAACJ;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAELnE,OAAA;gBAAI8D,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACvB/D,OAAA,CAACJ,QAAQ;kBAACwE,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACrB,IAAIW,IAAI,CAACvB,IAAI,CAACwB,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eAELnE,OAAA;gBAAI8D,SAAS,EAAC,cAAc;gBAAAC,QAAA,GACzB,CAACR,IAAI,CAACP,SAAS,iBACdhD,OAAA,CAAAE,SAAA;kBAAA6D,QAAA,GACG,CAACR,IAAI,CAACR,UAAU,gBACf/C,OAAA;oBACE8D,SAAS,EAAC,oBAAoB;oBAC9BmB,OAAO,EAAEA,CAAA,KAAM3C,gBAAgB,CAACiB,IAAI,CAAC2B,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAE;oBACzDC,KAAK,EAAC,cAAc;oBAAApB,QAAA,eAEpB/D,OAAA,CAACV,SAAS;sBAAC8E,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,gBAETnE,OAAA;oBACE8D,SAAS,EAAC,sBAAsB;oBAChCmB,OAAO,EAAEA,CAAA,KAAM3C,gBAAgB,CAACiB,IAAI,CAAC2B,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAE;oBAC1DC,KAAK,EAAC,gBAAgB;oBAAApB,QAAA,eAEtB/D,OAAA,CAACT,KAAK;sBAAC6E,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CACT,eAEDnE,OAAA;oBACE8D,SAAS,EAAC,kBAAkB;oBAC5BmB,OAAO,EAAEA,CAAA,KAAM3C,gBAAgB,CAACiB,IAAI,CAAC2B,EAAE,EAAE,OAAO,EAAE,IAAI,CAAE;oBACxDC,KAAK,EAAC,YAAY;oBAAApB,QAAA,eAElB/D,OAAA,CAACP,MAAM;sBAAC2E,IAAI,EAAE;oBAAG;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA,eACT,CACH,EAEAZ,IAAI,CAACP,SAAS,iBACbhD,OAAA;kBACE8D,SAAS,EAAC,oBAAoB;kBAC9BmB,OAAO,EAAEA,CAAA,KAAM3C,gBAAgB,CAACiB,IAAI,CAAC2B,EAAE,EAAE,OAAO,EAAE,KAAK,CAAE;kBACzDC,KAAK,EAAC,cAAc;kBAAApB,QAAA,eAEpB/D,OAAA,CAACV,SAAS;oBAAC8E,IAAI,EAAE;kBAAG;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CACT,eAEDnE,OAAA;kBACE8D,SAAS,EAAC,mBAAmB;kBAC7BmB,OAAO,EAAEA,CAAA,KAAM3C,gBAAgB,CAACiB,IAAI,CAAC2B,EAAE,EAAE,QAAQ,CAAE;kBACnDC,KAAK,EAAC,aAAa;kBAAApB,QAAA,eAEnB/D,OAAA,CAACR,MAAM;oBAAC4E,IAAI,EAAE;kBAAG;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GA/EEZ,IAAI,CAAC2B,EAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgFZ,CAAC;UAET,CAAC,CAAC,gBAEFnE,OAAA;YAAA+D,QAAA,eACE/D,OAAA;cAAIoF,OAAO,EAAC,GAAG;cAACtB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLpD,UAAU,CAACK,KAAK,GAAG,CAAC,iBACnBpB,OAAA;MAAK8D,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB/D,OAAA;QACE8D,SAAS,EAAC,gBAAgB;QAC1BuB,QAAQ,EAAEtE,UAAU,CAACE,IAAI,KAAK,CAAE;QAChCgE,OAAO,EAAEA,CAAA,KAAMjE,aAAa,CAACmB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAElB,IAAI,EAAEkB,IAAI,CAAClB,IAAI,GAAG;QAAE,CAAC,CAAC,CAAE;QAAA8C,QAAA,EAC1E;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETnE,OAAA;QAAM8D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAAC,OAC3B,EAAChD,UAAU,CAACE,IAAI,EAAC,MAAI,EAACF,UAAU,CAACK,KAAK,EAAC,IAAE,EAACL,UAAU,CAACI,KAAK,EAAC,SAClE;MAAA;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEPnE,OAAA;QACE8D,SAAS,EAAC,gBAAgB;QAC1BuB,QAAQ,EAAEtE,UAAU,CAACE,IAAI,KAAKF,UAAU,CAACK,KAAM;QAC/C6D,OAAO,EAAEA,CAAA,KAAMjE,aAAa,CAACmB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAElB,IAAI,EAAEkB,IAAI,CAAClB,IAAI,GAAG;QAAE,CAAC,CAAC,CAAE;QAAA8C,QAAA,EAC1E;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAGDnE,OAAA;MAAK8D,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvB/D,OAAA,CAACH,aAAa;QAACuE,IAAI,EAAE;MAAG;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3BnE,OAAA;QAAA+D,QAAA,gBACE/D,OAAA;UAAA+D,QAAA,EAAQ;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvCnE,OAAA;UAAA+D,QAAA,gBACE/D,OAAA;YAAA+D,QAAA,gBAAI/D,OAAA;cAAA+D,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,yDAAqD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvFnE,OAAA;YAAA+D,QAAA,gBAAI/D,OAAA;cAAA+D,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,+CAA2C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3EnE,OAAA;YAAA+D,QAAA,gBAAI/D,OAAA;cAAA+D,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,8DAA0D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/D,EAAA,CAvSID,WAAW;AAAAmF,EAAA,GAAXnF,WAAW;AAySjB,eAAeA,WAAW;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}