/*
 * Perfect Input Fields CSS
 * Solves all common input field issues:
 * - Icon and text overlap
 * - Placeholder visibility
 * - Floating labels
 * - Proper spacing
 * - Multiple states
 *
 * USAGE EXAMPLES:
 *
 * 1. Regular Input with Icon:
 * <div className="form-group">
 *   <label className="form-label">Email</label>
 *   <div className="input-wrapper">
 *     <MailIcon className="input-icon" />
 *     <input className="form-input" placeholder="Enter email" />
 *   </div>
 * </div>
 *
 * 2. Floating Label with Icon:
 * <div className="floating-label-wrapper">
 *   <div className="input-wrapper">
 *     <MailIcon className="input-icon" />
 *     <input className="form-input" placeholder=" " />
 *     <label className="floating-label">Email Address</label>
 *   </div>
 * </div>
 *
 * 3. Password with Toggle:
 * <div className="input-wrapper">
 *   <LockIcon className="input-icon" />
 *   <input type="password" className="form-input" />
 *   <button className="password-toggle">
 *     <EyeIcon />
 *   </button>
 * </div>
 */

/* Base Form Styling */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.form-label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

/* Input Wrapper for Icon Positioning */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

/* Icon Styling - Properly Positioned */
.input-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  z-index: 2;
  pointer-events: none;
  transition: color 0.2s ease;
}

/* Base Input Styling */
.form-input,
.form-select {
  width: 100%;
  padding: 16px 50px 16px 60px; /* Right padding for buttons, left for icons */
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
  color: #333;
  line-height: 1.5;
  font-family: inherit;
}

/* Input with icon - ensure proper spacing */
.input-wrapper .form-input {
  padding-left: 60px; /* Extra space for icon */
}

/* Input without icon */
.form-input.no-icon {
  padding-left: 16px;
}

/* Select Dropdown Styling */
.form-select {
  padding-left: 16px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 16px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 50px;
  appearance: none;
}

/* Focus States */
.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Icon color change on focus */
.input-wrapper:focus-within .input-icon {
  color: #667eea;
}

/* Placeholder Styling - Proper Visibility */
.form-input::placeholder {
  color: #9ca3af;
  opacity: 1;
  transition: opacity 0.2s ease;
}

.form-input:focus::placeholder {
  opacity: 0.7;
}

/* When input has value, hide placeholder completely */
.form-input:not(:placeholder-shown)::placeholder {
  opacity: 0;
}

/* Password Toggle Button */
.password-toggle {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border-radius: 6px;
  z-index: 2;
}

.password-toggle:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.05);
}

.password-toggle:active {
  transform: translateY(-50%) scale(0.95);
}

/* Enhanced Input States */
.form-input:hover:not(:focus) {
  border-color: #cbd5e0;
}

/* Error State */
.form-input.error {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.form-input.error:focus {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.2);
}

/* Success State */
.form-input.success {
  border-color: #38a169;
  box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
}

/* Disabled State */
.form-input:disabled {
  background-color: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
  border-color: #e2e8f0;
}

.form-input:disabled::placeholder {
  color: #cbd5e0;
}

/* FLOATING LABELS IMPLEMENTATION */

/* Floating Label Wrapper */
.floating-label-wrapper {
  position: relative;
  margin-bottom: 20px;
}

/* Floating Label */
.floating-label {
  position: absolute;
  left: 60px; /* Account for icon space */
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 1rem;
  pointer-events: none;
  transition: all 0.2s ease;
  background: white;
  padding: 0 4px;
  z-index: 1;
}

/* Floating Label Animation - When Focused or Has Value */
.floating-label-wrapper .form-input:focus + .floating-label,
.floating-label-wrapper .form-input:not(:placeholder-shown) + .floating-label {
  top: 0;
  left: 56px;
  font-size: 0.8rem;
  color: #667eea;
  font-weight: 500;
}

/* Floating Label - No Icon Variant */
.floating-label-wrapper.no-icon .floating-label {
  left: 16px;
}

.floating-label-wrapper.no-icon .form-input:focus + .floating-label,
.floating-label-wrapper.no-icon .form-input:not(:placeholder-shown) + .floating-label {
  left: 12px;
}

/* Floating Label Input Styling */
.floating-label-wrapper .form-input {
  padding-top: 20px;
  padding-bottom: 12px;
}

.floating-label-wrapper.no-icon .form-input {
  padding-left: 16px;
}

/* RESPONSIVE DESIGN */
@media (max-width: 768px) {
  .form-input,
  .form-select {
    padding: 14px 45px 14px 55px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  .input-icon {
    left: 16px;
  }
  
  .password-toggle {
    right: 16px;
  }
  
  .floating-label {
    left: 55px;
  }
  
  .floating-label-wrapper .form-input:focus + .floating-label,
  .floating-label-wrapper .form-input:not(:placeholder-shown) + .floating-label {
    left: 51px;
  }
}

/* UTILITY CLASSES */

/* Remove icon spacing when no icon is present */
.no-icon-spacing {
  padding-left: 16px !important;
}

/* Full width inputs */
.input-full-width {
  width: 100%;
}

/* Compact inputs */
.input-compact {
  padding: 12px 45px 12px 55px;
}

.input-compact.no-icon {
  padding: 12px 16px;
}

/* Large inputs */
.input-large {
  padding: 20px 55px 20px 65px;
  font-size: 1.1rem;
}

.input-large.no-icon {
  padding: 20px 20px;
}

/* DARK MODE SUPPORT */
@media (prefers-color-scheme: dark) {
  .form-input,
  .form-select {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .form-input:focus,
  .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
  }
  
  .form-input::placeholder {
    color: #a0aec0;
  }
  
  .input-icon {
    color: #a0aec0;
  }
  
  .floating-label {
    color: #a0aec0;
    background: #2d3748;
  }
  
  .form-label {
    color: #e2e8f0;
  }
}
