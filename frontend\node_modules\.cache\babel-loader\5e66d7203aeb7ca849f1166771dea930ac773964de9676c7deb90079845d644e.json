{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Companion\\\\frontend\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Mail, Lock, Eye, EyeOff, Heart } from 'lucide-react';\nimport axios from 'axios';\nimport './Auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = ({\n  onLogin\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/login', formData);\n      if (response.data.success) {\n        onLogin(response.data.data.user, response.data.data.token);\n        navigate('/dashboard');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Login error:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed. Please check your credentials and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-logo\",\n          children: [/*#__PURE__*/_jsxDEV(Heart, {\n            className: \"logo-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Companion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"auth-title\",\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-subtitle\",\n          children: \"Sign in to your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"auth-form\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            className: \"form-label\",\n            children: \"Email Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(Mail, {\n              className: \"input-icon\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              id: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleChange,\n              className: \"form-input\",\n              placeholder: \"Enter your email\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            className: \"form-label\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(Lock, {\n              className: \"input-icon\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: showPassword ? 'text' : 'password',\n              id: \"password\",\n              name: \"password\",\n              value: formData.password,\n              onChange: handleChange,\n              className: \"form-input\",\n              placeholder: \"Enter your password\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"password-toggle\",\n              onClick: () => setShowPassword(!showPassword),\n              children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 33\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 56\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"auth-button\",\n          disabled: loading,\n          children: loading ? 'Signing In...' : 'Sign In'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/signup\",\n            className: \"auth-link\",\n            children: \"Sign up here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"WAURFNneQ1vniA/zJ0EvUSfRKvU=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["useState", "Link", "useNavigate", "Mail", "Lock", "Eye", "Eye<PERSON>ff", "Heart", "axios", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "onLogin", "_s", "formData", "setFormData", "email", "password", "showPassword", "setShowPassword", "loading", "setLoading", "error", "setError", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "response", "post", "data", "success", "user", "token", "_error$response", "_error$response$data", "console", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "size", "type", "id", "onChange", "placeholder", "required", "onClick", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Companion/frontend/src/components/Login.js"], "sourcesContent": ["import { useState } from 'react';\nimport { <PERSON>, useNavigate } from 'react-router-dom';\nimport { Mail, Lock, Eye, EyeOff, Heart } from 'lucide-react';\nimport axios from 'axios';\nimport './Auth.css';\n\nconst Login = ({ onLogin }) => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/login', formData);\n      \n      if (response.data.success) {\n        onLogin(response.data.data.user, response.data.data.token);\n        navigate('/dashboard');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      setError(\n        error.response?.data?.message || \n        'Login failed. Please check your credentials and try again.'\n      );\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <div className=\"auth-logo\">\n            <Heart className=\"logo-icon\" />\n            <span>Companion</span>\n          </div>\n          <h2 className=\"auth-title\">Welcome Back</h2>\n          <p className=\"auth-subtitle\">Sign in to your account</p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"auth-form\">\n          {error && (\n            <div className=\"error-message\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"form-group\">\n            <label htmlFor=\"email\" className=\"form-label\">\n              Email Address\n            </label>\n            <div className=\"input-wrapper\">\n              <Mail className=\"input-icon\" size={20} />\n              <input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleChange}\n                className=\"form-input\"\n                placeholder=\"Enter your email\"\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\" className=\"form-label\">\n              Password\n            </label>\n            <div className=\"input-wrapper\">\n              <Lock className=\"input-icon\" size={20} />\n              <input\n                type={showPassword ? 'text' : 'password'}\n                id=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                className=\"form-input\"\n                placeholder=\"Enter your password\"\n                required\n              />\n              <button\n                type=\"button\"\n                className=\"password-toggle\"\n                onClick={() => setShowPassword(!showPassword)}\n              >\n                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}\n              </button>\n            </div>\n          </div>\n\n          <button\n            type=\"submit\"\n            className=\"auth-button\"\n            disabled={loading}\n          >\n            {loading ? 'Signing In...' : 'Sign In'}\n          </button>\n        </form>\n\n        <div className=\"auth-footer\">\n          <p>\n            Don't have an account?{' '}\n            <Link to=\"/signup\" className=\"auth-link\">\n              Sign up here\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,QAAQ,cAAc;AAC7D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC;IACvCgB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMwB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAMuB,YAAY,GAAIC,CAAC,IAAK;IAC1BX,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACY,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;IACF;IACA,IAAIP,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBV,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,IAAI,CAAC,sCAAsC,EAAEnB,QAAQ,CAAC;MAEnF,IAAIkB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBvB,OAAO,CAACoB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACE,IAAI,EAAEJ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACG,KAAK,CAAC;QAC1Db,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MAAA,IAAAgB,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAAClB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CACN,EAAAe,eAAA,GAAAhB,KAAK,CAACU,QAAQ,cAAAM,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBJ,IAAI,cAAAK,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAC7B,4DACF,CAAC;IACH,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEX,OAAA;IAAKgC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BjC,OAAA;MAAKgC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBjC,OAAA;QAAKgC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjC,OAAA;UAAKgC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBjC,OAAA,CAACH,KAAK;YAACmC,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BrC,OAAA;YAAAiC,QAAA,EAAM;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNrC,OAAA;UAAIgC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CrC,OAAA;UAAGgC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eAENrC,OAAA;QAAMsC,QAAQ,EAAElB,YAAa;QAACY,SAAS,EAAC,WAAW;QAAAC,QAAA,GAChDrB,KAAK,iBACJZ,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BrB;QAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBjC,OAAA;YAAOuC,OAAO,EAAC,OAAO;YAACP,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrC,OAAA;YAAKgC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BjC,OAAA,CAACP,IAAI;cAACuC,SAAS,EAAC,YAAY;cAACQ,IAAI,EAAE;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCrC,OAAA;cACEyC,IAAI,EAAC,OAAO;cACZC,EAAE,EAAC,OAAO;cACVxB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEf,QAAQ,CAACE,KAAM;cACtBqC,QAAQ,EAAE5B,YAAa;cACvBiB,SAAS,EAAC,YAAY;cACtBY,WAAW,EAAC,kBAAkB;cAC9BC,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBjC,OAAA;YAAOuC,OAAO,EAAC,UAAU;YAACP,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrC,OAAA;YAAKgC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BjC,OAAA,CAACN,IAAI;cAACsC,SAAS,EAAC,YAAY;cAACQ,IAAI,EAAE;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCrC,OAAA;cACEyC,IAAI,EAAEjC,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCkC,EAAE,EAAC,UAAU;cACbxB,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEf,QAAQ,CAACG,QAAS;cACzBoC,QAAQ,EAAE5B,YAAa;cACvBiB,SAAS,EAAC,YAAY;cACtBY,WAAW,EAAC,qBAAqB;cACjCC,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFrC,OAAA;cACEyC,IAAI,EAAC,QAAQ;cACbT,SAAS,EAAC,iBAAiB;cAC3Bc,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAAC,CAACD,YAAY,CAAE;cAAAyB,QAAA,EAE7CzB,YAAY,gBAAGR,OAAA,CAACJ,MAAM;gBAAC4C,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGrC,OAAA,CAACL,GAAG;gBAAC6C,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrC,OAAA;UACEyC,IAAI,EAAC,QAAQ;UACbT,SAAS,EAAC,aAAa;UACvBe,QAAQ,EAAErC,OAAQ;UAAAuB,QAAA,EAEjBvB,OAAO,GAAG,eAAe,GAAG;QAAS;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPrC,OAAA;QAAKgC,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BjC,OAAA;UAAAiC,QAAA,GAAG,wBACqB,EAAC,GAAG,eAC1BjC,OAAA,CAACT,IAAI;YAACyD,EAAE,EAAC,SAAS;YAAChB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CA9HIF,KAAK;EAAA,QAQQT,WAAW;AAAA;AAAAyD,EAAA,GARxBhD,KAAK;AAgIX,eAAeA,KAAK;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}