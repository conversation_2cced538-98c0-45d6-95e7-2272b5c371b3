import { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Plus,
  Search,
  Filter,
  Calendar,
  MapPin,
  Clock,
  DollarSign,
  User,
  Heart,
  Star,
  MessageCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import CreateServiceRequest from './CreateServiceRequest';
import './UserDashboard.css';

const UserDashboard = ({ user }) => {
  const [activeTab, setActiveTab] = useState('browse');
  const [serviceRequests, setServiceRequests] = useState([]);
  const [myRequests, setMyRequests] = useState([]);
  const [myApplications, setMyApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Fetch data from API
  useEffect(() => {
    fetchServiceRequests();
    if (user.role === 'user') {
      fetchMyRequests();
    } else if (user.role === 'caregiver') {
      fetchMyApplications();
    }
  }, [user.role, selectedCategory, searchTerm]);

  const fetchServiceRequests = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        category: selectedCategory,
        search: searchTerm,
        limit: 20
      });

      const response = await axios.get(`http://localhost:5000/api/services/requests?${params}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setServiceRequests(response.data.data.requests);
      }
    } catch (error) {
      console.error('Error fetching service requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchMyRequests = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5000/api/services/my-requests', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setMyRequests(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching my requests:', error);
    }
  };

  const fetchMyApplications = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5000/api/services/my-applications', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setMyApplications(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching my applications:', error);
    }
  };

  const handleApplyForService = async (requestId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        `http://localhost:5000/api/services/requests/${requestId}/apply`,
        { message: 'I am interested in this opportunity and would love to help.' },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.data.success) {
        alert('Application submitted successfully!');
        fetchServiceRequests(); // Refresh the list
        fetchMyApplications(); // Refresh applications if caregiver
      }
    } catch (error) {
      console.error('Error applying for service:', error);
      alert(error.response?.data?.message || 'Failed to submit application');
    }
  };

  const categories = [
    'all',
    'Baby Care',
    'Elderly Care',
    'Disability Support',
    'Reading & Writing Help',
    'Household Help',
    'Medical Assistance'
  ];

  const filteredRequests = serviceRequests.filter(request => {
    const matchesSearch = request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || request.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const ServiceRequestCard = ({ request, showActions = true }) => (
    <div className="service-card">
      <div className="service-header">
        <div className="service-title-section">
          <h3 className="service-title">{request.title}</h3>
          <span className="service-category">{request.category}</span>
        </div>
        <div className="service-rate">
          <DollarSign size={16} />
          <span>${request.hourlyRate}/hr</span>
        </div>
      </div>
      
      <p className="service-description">{request.description}</p>
      
      <div className="service-details">
        <div className="detail-item">
          <MapPin size={16} />
          <span>{request.location}</span>
        </div>
        <div className="detail-item">
          <Calendar size={16} />
          <span>{new Date(request.startDate).toLocaleDateString()} - {new Date(request.endDate).toLocaleDateString()}</span>
        </div>
        <div className="detail-item">
          <User size={16} />
          <span>{request.clientName}</span>
          <div className="rating">
            <Star size={14} fill="currentColor" />
            <span>{request.clientRating}</span>
          </div>
        </div>
      </div>
      
      <div className="service-footer">
        <div className="service-stats">
          <span className="applications-count">{request.applicationsCount} applications</span>
          <span className={`status ${request.status}`}>{request.status}</span>
        </div>
        
        {showActions && (
          <div className="service-actions">
            {user.role === 'caregiver' ? (
              <button
                className="btn btn-primary"
                onClick={() => handleApplyForService(request.id)}
              >
                <Heart size={16} />
                Apply Now
              </button>
            ) : (
              <button className="btn btn-secondary">
                <MessageCircle size={16} />
                Contact
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );

  const TabButton = ({ id, label, icon, isActive, onClick }) => (
    <button
      className={`tab-button ${isActive ? 'active' : ''}`}
      onClick={() => onClick(id)}
    >
      {icon}
      <span>{label}</span>
    </button>
  );

  return (
    <div className="user-dashboard">
      <div className="dashboard-container">
        <div className="dashboard-header">
          <h1 className="dashboard-title">
            Welcome back, {user.firstName}!
          </h1>
          <p className="dashboard-subtitle">
            {user.role === 'caregiver' 
              ? 'Find meaningful opportunities to help others in your community'
              : 'Connect with trusted caregivers for your needs'
            }
          </p>
        </div>

        <div className="dashboard-tabs">
          <TabButton
            id="browse"
            label="Browse Services"
            icon={<Search size={20} />}
            isActive={activeTab === 'browse'}
            onClick={setActiveTab}
          />
          {user.role === 'user' && (
            <TabButton
              id="my-requests"
              label="My Requests"
              icon={<Calendar size={20} />}
              isActive={activeTab === 'my-requests'}
              onClick={setActiveTab}
            />
          )}
          {user.role === 'caregiver' && (
            <TabButton
              id="my-applications"
              label="My Applications"
              icon={<Heart size={20} />}
              isActive={activeTab === 'my-applications'}
              onClick={setActiveTab}
            />
          )}
          <TabButton
            id="profile"
            label="Profile"
            icon={<User size={20} />}
            isActive={activeTab === 'profile'}
            onClick={setActiveTab}
          />
        </div>

        <div className="dashboard-content">
          {activeTab === 'browse' && (
            <div className="browse-section">
              <div className="browse-header">
                <div className="search-filters">
                  <div className="search-box">
                    <Search size={20} />
                    <input
                      type="text"
                      placeholder="Search services..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="category-filter"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>
                        {category === 'all' ? 'All Categories' : category}
                      </option>
                    ))}
                  </select>
                </div>
                
                {user.role === 'user' && (
                  <button
                    className="btn btn-primary"
                    onClick={() => setShowCreateModal(true)}
                  >
                    <Plus size={20} />
                    Post New Request
                  </button>
                )}
              </div>

              <div className="services-grid">
                {loading ? (
                  <div className="loading-state">
                    <div className="loading-spinner"></div>
                    <p>Loading opportunities...</p>
                  </div>
                ) : filteredRequests.length > 0 ? (
                  filteredRequests.map(request => (
                    <ServiceRequestCard key={request.id} request={request} />
                  ))
                ) : (
                  <div className="empty-state">
                    <Search size={48} />
                    <h3>No services found</h3>
                    <p>Try adjusting your search criteria or check back later for new opportunities.</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'my-requests' && user.role === 'user' && (
            <div className="my-requests-section">
              <div className="section-header">
                <h2>My Service Requests</h2>
                <button
                  className="btn btn-primary"
                  onClick={() => setShowCreateModal(true)}
                >
                  <Plus size={20} />
                  Create New Request
                </button>
              </div>

              <div className="requests-list">
                {myRequests.length > 0 ? (
                  <div className="services-grid">
                    {myRequests.map(request => (
                      <ServiceRequestCard key={request.id} request={request} showActions={false} />
                    ))}
                  </div>
                ) : (
                  <div className="empty-state">
                    <Calendar size={48} />
                    <h3>No requests yet</h3>
                    <p>Create your first service request to get started.</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'my-applications' && user.role === 'caregiver' && (
            <div className="my-applications-section">
              <div className="section-header">
                <h2>My Applications</h2>
              </div>

              <div className="applications-list">
                {myApplications.length > 0 ? (
                  <div className="services-grid">
                    {myApplications.map(application => (
                      <div key={application.id} className="service-card application-card">
                        <div className="service-header">
                          <div className="service-title-section">
                            <h3 className="service-title">{application.title}</h3>
                            <span className="service-category">{application.category}</span>
                          </div>
                          <div className="application-status">
                            <span className={`status ${application.status}`}>
                              {application.status}
                            </span>
                          </div>
                        </div>

                        <p className="service-description">{application.description}</p>

                        <div className="service-details">
                          <div className="detail-item">
                            <MapPin size={16} />
                            <span>{application.location}</span>
                          </div>
                          <div className="detail-item">
                            <Calendar size={16} />
                            <span>{new Date(application.startDate).toLocaleDateString()}</span>
                          </div>
                          <div className="detail-item">
                            <User size={16} />
                            <span>{application.clientName}</span>
                          </div>
                          {application.proposedRate && (
                            <div className="detail-item">
                              <DollarSign size={16} />
                              <span>Proposed: ${application.proposedRate}/hr</span>
                            </div>
                          )}
                        </div>

                        <div className="application-footer">
                          <small className="applied-date">
                            Applied: {new Date(application.appliedAt).toLocaleDateString()}
                          </small>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="empty-state">
                    <Heart size={48} />
                    <h3>No applications yet</h3>
                    <p>Start applying to service requests to help others in your community.</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'profile' && (
            <div className="profile-section">
              <div className="section-header">
                <h2>Profile Settings</h2>
              </div>
              
              <div className="profile-content">
                <div className="profile-card">
                  <div className="profile-avatar">
                    <User size={48} />
                  </div>
                  <div className="profile-info">
                    <h3>{user.firstName} {user.lastName}</h3>
                    <p>{user.email}</p>
                    <span className="role-badge">{user.role}</span>
                  </div>
                  <button className="btn btn-secondary">Edit Profile</button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <CreateServiceRequest
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={() => {
          fetchMyRequests();
          fetchServiceRequests();
        }}
      />
    </div>
  );
};

export default UserDashboard;
