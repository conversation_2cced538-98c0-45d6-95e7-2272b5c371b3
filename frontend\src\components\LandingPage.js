import { Link } from 'react-router-dom';
import { <PERSON>, Baby, Users, BookOpen, Home, Stethoscope, Star } from 'lucide-react';
import './LandingPage.css';

const LandingPage = () => {
  const services = [
    {
      icon: <Baby size={40} />,
      title: "Baby Care",
      description: "Professional baby sitting and child care services for your little ones."
    },
    {
      icon: <Heart size={40} />,
      title: "Elderly Care",
      description: "Compassionate care and companionship for elderly family members."
    },
    {
      icon: <Users size={40} />,
      title: "Disability Support",
      description: "Specialized assistance for individuals with disabilities."
    },
    {
      icon: <BookOpen size={40} />,
      title: "Reading & Writing Help",
      description: "Educational support and literacy assistance for all ages."
    },
    {
      icon: <Home size={40} />,
      title: "Household Help",
      description: "General household assistance and daily living support."
    },
    {
      icon: <Stethoscope size={40} />,
      title: "Medical Assistance",
      description: "Basic medical support and health monitoring services."
    }
  ];

  const quotes = [
    {
      text: "Caring is not just a profession, it's a calling to make a difference in someone's life.",
      author: "Anonymous"
    },
    {
      text: "The best way to find yourself is to lose yourself in the service of others.",
      author: "Mahatma Gandhi"
    },
    {
      text: "We make a living by what we get, but we make a life by what we give.",
      author: "<PERSON> Churchill"
    },
    {
      text: "No act of kindness, no matter how small, is ever wasted.",
      author: "Aesop"
    }
  ];

  return (
    <div className="landing-page">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-container">
          <div className="hero-content">
            <h1 className="hero-title">
              Your Trusted <span className="highlight">Companion</span> for Everyday Care
            </h1>
            <p className="hero-subtitle">
              A marketplace for everyday help and care - connecting those who need assistance 
              with compassionate caregivers in your community.
            </p>
            <div className="hero-buttons">
              <Link to="/signup" className="cta-button primary">
                Get Started
              </Link>
              <Link to="/login" className="cta-button secondary">
                Sign In
              </Link>
            </div>
          </div>
          <div className="hero-image">
            <div className="hero-illustration">
              <Heart size={120} className="hero-heart" />
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="services">
        <div className="container">
          <h2 className="section-title">Our Care Services</h2>
          <p className="section-subtitle">
            Professional care and assistance tailored to your unique needs
          </p>
          <div className="services-grid">
            {services.map((service, index) => (
              <div key={index} className="service-card">
                <div className="service-icon">
                  {service.icon}
                </div>
                <h3 className="service-title">{service.title}</h3>
                <p className="service-description">{service.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Quotes Section */}
      <section className="quotes">
        <div className="container">
          <h2 className="section-title">Words of Inspiration</h2>
          <div className="quotes-grid">
            {quotes.map((quote, index) => (
              <div key={index} className="quote-card">
                <div className="quote-icon">
                  <Star size={24} />
                </div>
                <blockquote className="quote-text">
                  "{quote.text}"
                </blockquote>
                <cite className="quote-author">— {quote.author}</cite>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2 className="cta-title">Ready to Make a Difference?</h2>
            <p className="cta-text">
              Join our community of caregivers and care seekers. Whether you need help 
              or want to help others, we're here to connect you.
            </p>
            <div className="cta-buttons">
              <Link to="/signup" className="cta-button primary large">
                Join Our Community
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;
