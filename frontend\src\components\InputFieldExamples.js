import { useState } from 'react';
import { Mail, Lock, User, Phone, Eye, EyeOff, Search } from 'lucide-react';
import './Auth.css';

const InputFieldExamples = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    firstName: '',
    phone: '',
    search: ''
  });
  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="auth-container">
      <div className="auth-card" style={{ maxWidth: '600px' }}>
        <div className="auth-header">
          <h2 className="auth-title">Input Field Examples</h2>
          <p className="auth-subtitle">Demonstrating proper spacing and functionality</p>
        </div>

        <div className="auth-form">
          <h3 style={{ marginBottom: '20px', color: '#333' }}>Regular Input Fields</h3>
          
          {/* Email with icon */}
          <div className="form-group">
            <label htmlFor="email" className="form-label">
              Email Address
            </label>
            <div className="input-wrapper">
              <Mail className="input-icon" size={20} />
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="form-input"
                placeholder="Enter your email address"
              />
            </div>
          </div>

          {/* Password with icon and toggle */}
          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Password
            </label>
            <div className="input-wrapper">
              <Lock className="input-icon" size={20} />
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className="form-input"
                placeholder="Enter your password"
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
          </div>

          {/* Input without icon */}
          <div className="form-group">
            <label htmlFor="firstName" className="form-label">
              First Name (No Icon)
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              className="form-input has-icon"
              style={{ paddingLeft: '16px' }}
              placeholder="Enter your first name"
            />
          </div>

          <h3 style={{ marginTop: '40px', marginBottom: '20px', color: '#333' }}>Floating Label Fields</h3>
          
          {/* Floating label with icon */}
          <div className="floating-label-wrapper">
            <div className="input-wrapper">
              <Phone className="input-icon" size={20} />
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className="form-input"
                placeholder=" "
              />
              <label htmlFor="phone" className="floating-label">
                Phone Number
              </label>
            </div>
          </div>

          {/* Floating label without icon */}
          <div className="floating-label-wrapper no-icon">
            <div className="input-wrapper">
              <input
                type="text"
                id="search"
                name="search"
                value={formData.search}
                onChange={handleChange}
                className="form-input"
                placeholder=" "
              />
              <label htmlFor="search" className="floating-label">
                Search (No Icon)
              </label>
            </div>
          </div>

          {/* Select dropdown */}
          <div className="form-group">
            <label htmlFor="role" className="form-label">
              Role Selection
            </label>
            <select
              id="role"
              name="role"
              className="form-select"
            >
              <option value="">Select your role</option>
              <option value="user">Service Seeker</option>
              <option value="caregiver">Service Provider</option>
            </select>
          </div>

          {/* Input states demonstration */}
          <h3 style={{ marginTop: '40px', marginBottom: '20px', color: '#333' }}>Input States</h3>
          
          <div className="form-group">
            <label className="form-label">Normal State</label>
            <div className="input-wrapper">
              <User className="input-icon" size={20} />
              <input
                type="text"
                className="form-input"
                placeholder="Normal input field"
                defaultValue="Sample text"
              />
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Error State</label>
            <div className="input-wrapper">
              <Mail className="input-icon" size={20} />
              <input
                type="email"
                className="form-input error"
                placeholder="Error state input"
                defaultValue="invalid-email"
              />
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Success State</label>
            <div className="input-wrapper">
              <Mail className="input-icon" size={20} />
              <input
                type="email"
                className="form-input success"
                placeholder="Success state input"
                defaultValue="<EMAIL>"
              />
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Disabled State</label>
            <div className="input-wrapper">
              <User className="input-icon" size={20} />
              <input
                type="text"
                className="form-input"
                placeholder="Disabled input"
                defaultValue="Cannot edit this"
                disabled
              />
            </div>
          </div>

          {/* Demo information */}
          <div style={{ 
            marginTop: '40px', 
            padding: '20px', 
            background: '#f8f9fa', 
            borderRadius: '8px',
            border: '1px solid #e9ecef'
          }}>
            <h4 style={{ marginBottom: '10px', color: '#495057' }}>Key Features:</h4>
            <ul style={{ margin: 0, paddingLeft: '20px', color: '#6c757d' }}>
              <li>Icons properly positioned with adequate spacing</li>
              <li>Placeholder text disappears as you type</li>
              <li>Floating labels move above input when focused or filled</li>
              <li>Password toggle button positioned correctly</li>
              <li>Hover and focus states with smooth transitions</li>
              <li>Error, success, and disabled states</li>
              <li>Responsive design that works on all screen sizes</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InputFieldExamples;
