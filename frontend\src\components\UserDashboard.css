.user-dashboard {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20px 0;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
}

.dashboard-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
}

/* Tabs */
.dashboard-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  border-bottom: 2px solid #e9ecef;
  overflow-x: auto;
  padding-bottom: 0;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: none;
  color: #666;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
}

.tab-button:hover {
  background: #f8f9fa;
  color: #333;
}

.tab-button.active {
  background: white;
  color: #667eea;
  border-bottom: 2px solid #667eea;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: #667eea;
}

/* Browse Section */
.browse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  gap: 20px;
}

.search-filters {
  display: flex;
  gap: 15px;
  flex: 1;
  max-width: 600px;
}

.search-box {
  position: relative;
  flex: 1;
}

.search-box svg {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.search-box input {
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
}

.category-filter {
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  min-width: 180px;
}

.category-filter:focus {
  outline: none;
  border-color: #667eea;
}

/* Buttons */
.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: 0.95rem;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover {
  background: #cbd5e0;
}

/* Service Cards */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.service-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.service-title-section {
  flex: 1;
}

.service-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.service-category {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.service-rate {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #e8f5e8;
  color: #2e7d32;
  padding: 8px 12px;
  border-radius: 8px;
  font-weight: 600;
  white-space: nowrap;
}

.service-description {
  color: #666;
  line-height: 1.5;
  margin-bottom: 20px;
}

.service-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 0.9rem;
}

.detail-item svg {
  color: #667eea;
  flex-shrink: 0;
}

.rating {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
  color: #ffa726;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.service-stats {
  display: flex;
  gap: 15px;
  align-items: center;
}

.applications-count {
  color: #666;
  font-size: 0.9rem;
}

.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status.pending {
  background: #fff3e0;
  color: #f57c00;
}

.status.active {
  background: #e8f5e8;
  color: #2e7d32;
}

.status.completed {
  background: #e3f2fd;
  color: #1976d2;
}

.status.accepted {
  background: #e8f5e8;
  color: #2e7d32;
}

.status.rejected {
  background: #ffebee;
  color: #d32f2f;
}

/* Application Cards */
.application-card {
  border-left: 4px solid #667eea;
}

.application-status {
  display: flex;
  align-items: center;
}

.application-footer {
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
  margin-top: 15px;
}

.applied-date {
  color: #666;
  font-size: 0.85rem;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.section-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-state svg {
  color: #ccc;
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: #333;
}

.empty-state p {
  font-size: 1rem;
  margin: 0;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Profile Section */
.profile-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-avatar {
  width: 80px;
  height: 80px;
  background: #e3f2fd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1976d2;
}

.profile-info {
  flex: 1;
}

.profile-info h3 {
  font-size: 1.5rem;
  margin: 0 0 5px 0;
  color: #333;
}

.profile-info p {
  color: #666;
  margin: 0 0 10px 0;
}

.role-badge {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 0 15px;
  }
  
  .dashboard-title {
    font-size: 2rem;
  }
  
  .browse-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-filters {
    flex-direction: column;
    max-width: none;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .service-card {
    padding: 20px;
  }
  
  .service-footer {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .profile-card {
    flex-direction: column;
    text-align: center;
  }
  
  .dashboard-tabs {
    gap: 5px;
  }
  
  .tab-button {
    padding: 10px 15px;
    font-size: 0.9rem;
  }
}
