{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Companion\\\\frontend\\\\src\\\\components\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Users, Heart, Activity, TrendingUp, UserCheck, UserX } from 'lucide-react';\nimport ManageUsers from './ManageUsers';\nimport './AdminDashboard.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = ({\n  user\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    totalCaregivers: 0,\n    activeRequests: 0,\n    completedServices: 0\n  });\n  const [recentUsers, setRecentUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchAdminStats();\n    fetchRecentUsers();\n  }, []);\n  const fetchAdminStats = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/admin/stats', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setStats(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching admin stats:', error);\n    }\n  };\n  const fetchRecentUsers = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/admin/recent-users?limit=5', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setRecentUsers(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching recent users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleUserStatusUpdate = async (userId, isVerified) => {\n    try {\n      const token = localStorage.getItem('token');\n      await axios.patch(`http://localhost:5000/api/admin/users/${userId}/status`, {\n        isVerified\n      }, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n\n      // Refresh the users list\n      fetchRecentUsers();\n      fetchAdminStats();\n    } catch (error) {\n      console.error('Error updating user status:', error);\n      alert('Failed to update user status');\n    }\n  };\n  const StatCard = ({\n    icon,\n    title,\n    value,\n    color\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"stat-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `stat-icon ${color}`,\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"stat-value\",\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"stat-title\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-dashboard\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"dashboard-title\",\n          children: \"Admin Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"dashboard-subtitle\",\n          children: [\"Welcome back, \", user.firstName, \"! Here's what's happening in your community.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-button ${activeTab === 'dashboard' ? 'active' : ''}`,\n          onClick: () => setActiveTab('dashboard'),\n          children: [/*#__PURE__*/_jsxDEV(Activity, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), \"Dashboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-button ${activeTab === 'users' ? 'active' : ''}`,\n          onClick: () => setActiveTab('users'),\n          children: [/*#__PURE__*/_jsxDEV(Users, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), \"Manage Users\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), activeTab === 'dashboard' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stats-grid\",\n          children: [/*#__PURE__*/_jsxDEV(StatCard, {\n            icon: /*#__PURE__*/_jsxDEV(Users, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 19\n            }, this),\n            title: \"Total Users\",\n            value: stats.totalUsers,\n            color: \"blue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n            icon: /*#__PURE__*/_jsxDEV(Heart, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 19\n            }, this),\n            title: \"Caregivers\",\n            value: stats.totalCaregivers,\n            color: \"pink\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n            icon: /*#__PURE__*/_jsxDEV(Activity, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 19\n            }, this),\n            title: \"Active Requests\",\n            value: stats.activeRequests,\n            color: \"orange\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n            icon: /*#__PURE__*/_jsxDEV(TrendingUp, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 19\n            }, this),\n            title: \"Completed Services\",\n            value: stats.completedServices,\n            color: \"green\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"Recent Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"User Verification System:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 16\n              }, this), \" New users must be approved by admin before they can access the platform. Pending users can register but cannot login until verified.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 11\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-state\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading users...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-container\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"users-table\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Role\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: recentUsers.length > 0 ? recentUsers.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"user-name\",\n                    children: user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"user-email\",\n                    children: user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `role-badge ${user.role}`,\n                      children: user.role\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `status-badge ${user.status}`,\n                      children: user.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"user-actions\",\n                    children: user.status === 'pending' ? /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"action-btn approve\",\n                      onClick: () => handleUserStatusUpdate(user.id, true),\n                      title: \"Approve User\",\n                      children: /*#__PURE__*/_jsxDEV(UserCheck, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 192,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"action-btn reject\",\n                      onClick: () => handleUserStatusUpdate(user.id, false),\n                      title: \"Suspend User\",\n                      children: /*#__PURE__*/_jsxDEV(UserX, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 25\n                  }, this)]\n                }, user.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"5\",\n                    className: \"no-data\",\n                    children: \"No users found\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quick-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-card\",\n              children: [/*#__PURE__*/_jsxDEV(Users, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Manage Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-card\",\n              children: [/*#__PURE__*/_jsxDEV(Heart, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Review Services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-card\",\n              children: [/*#__PURE__*/_jsxDEV(Activity, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"View Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-card\",\n              children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Analytics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true), activeTab === 'users' && /*#__PURE__*/_jsxDEV(ManageUsers, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"yp7EURb2MP4m8QYO3NTP4Ckmx4o=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["useState", "useEffect", "axios", "Users", "Heart", "Activity", "TrendingUp", "UserCheck", "UserX", "ManageUsers", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminDashboard", "user", "_s", "activeTab", "setActiveTab", "stats", "setStats", "totalUsers", "totalCaregivers", "activeRequests", "completedServices", "recentUsers", "setRecentUsers", "loading", "setLoading", "fetchAdminStats", "fetchRecentUsers", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "data", "success", "error", "console", "handleUserStatusUpdate", "userId", "isVerified", "patch", "alert", "StatCard", "icon", "title", "value", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "onClick", "size", "length", "map", "name", "email", "role", "status", "id", "colSpan", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Companion/frontend/src/components/AdminDashboard.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Users, Heart, Activity, TrendingUp, UserCheck, UserX } from 'lucide-react';\nimport ManageUsers from './ManageUsers';\nimport './AdminDashboard.css';\n\nconst AdminDashboard = ({ user }) => {\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    totalCaregivers: 0,\n    activeRequests: 0,\n    completedServices: 0\n  });\n  const [recentUsers, setRecentUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchAdminStats();\n    fetchRecentUsers();\n  }, []);\n\n  const fetchAdminStats = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/admin/stats', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setStats(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching admin stats:', error);\n    }\n  };\n\n  const fetchRecentUsers = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/admin/recent-users?limit=5', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setRecentUsers(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching recent users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleUserStatusUpdate = async (userId, isVerified) => {\n    try {\n      const token = localStorage.getItem('token');\n      await axios.patch(`http://localhost:5000/api/admin/users/${userId}/status`,\n        { isVerified },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n\n      // Refresh the users list\n      fetchRecentUsers();\n      fetchAdminStats();\n    } catch (error) {\n      console.error('Error updating user status:', error);\n      alert('Failed to update user status');\n    }\n  };\n\n\n\n  const StatCard = ({ icon, title, value, color }) => (\n    <div className=\"stat-card\">\n      <div className={`stat-icon ${color}`}>\n        {icon}\n      </div>\n      <div className=\"stat-content\">\n        <h3 className=\"stat-value\">{value}</h3>\n        <p className=\"stat-title\">{title}</p>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"admin-dashboard\">\n      <div className=\"dashboard-container\">\n        <div className=\"dashboard-header\">\n          <h1 className=\"dashboard-title\">Admin Dashboard</h1>\n          <p className=\"dashboard-subtitle\">\n            Welcome back, {user.firstName}! Here's what's happening in your community.\n          </p>\n        </div>\n\n        {/* Navigation Tabs */}\n        <div className=\"admin-tabs\">\n          <button\n            className={`tab-button ${activeTab === 'dashboard' ? 'active' : ''}`}\n            onClick={() => setActiveTab('dashboard')}\n          >\n            <Activity size={20} />\n            Dashboard\n          </button>\n          <button\n            className={`tab-button ${activeTab === 'users' ? 'active' : ''}`}\n            onClick={() => setActiveTab('users')}\n          >\n            <Users size={20} />\n            Manage Users\n          </button>\n        </div>\n\n        {/* Dashboard Content */}\n        {activeTab === 'dashboard' && (\n          <>\n            {/* Stats Grid */}\n            <div className=\"stats-grid\">\n          <StatCard\n            icon={<Users size={24} />}\n            title=\"Total Users\"\n            value={stats.totalUsers}\n            color=\"blue\"\n          />\n          <StatCard\n            icon={<Heart size={24} />}\n            title=\"Caregivers\"\n            value={stats.totalCaregivers}\n            color=\"pink\"\n          />\n          <StatCard\n            icon={<Activity size={24} />}\n            title=\"Active Requests\"\n            value={stats.activeRequests}\n            color=\"orange\"\n          />\n          <StatCard\n            icon={<TrendingUp size={24} />}\n            title=\"Completed Services\"\n            value={stats.completedServices}\n            color=\"green\"\n          />\n        </div>\n\n        {/* Recent Users Table */}\n        <div className=\"dashboard-section\">\n          <h2 className=\"section-title\">Recent Users</h2>\n          <div className=\"section-info\">\n            <p><strong>User Verification System:</strong> New users must be approved by admin before they can access the platform.\n            Pending users can register but cannot login until verified.</p>\n          </div>\n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"loading-spinner\"></div>\n              <p>Loading users...</p>\n            </div>\n          ) : (\n            <div className=\"table-container\">\n              <table className=\"users-table\">\n                <thead>\n                  <tr>\n                    <th>Name</th>\n                    <th>Email</th>\n                    <th>Role</th>\n                    <th>Status</th>\n                    <th>Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {recentUsers.length > 0 ? (\n                    recentUsers.map(user => (\n                      <tr key={user.id}>\n                        <td className=\"user-name\">{user.name}</td>\n                        <td className=\"user-email\">{user.email}</td>\n                        <td>\n                          <span className={`role-badge ${user.role}`}>\n                            {user.role}\n                          </span>\n                        </td>\n                        <td>\n                          <span className={`status-badge ${user.status}`}>\n                            {user.status}\n                          </span>\n                        </td>\n                        <td className=\"user-actions\">\n                          {user.status === 'pending' ? (\n                            <button\n                              className=\"action-btn approve\"\n                              onClick={() => handleUserStatusUpdate(user.id, true)}\n                              title=\"Approve User\"\n                            >\n                              <UserCheck size={16} />\n                            </button>\n                          ) : (\n                            <button\n                              className=\"action-btn reject\"\n                              onClick={() => handleUserStatusUpdate(user.id, false)}\n                              title=\"Suspend User\"\n                            >\n                              <UserX size={16} />\n                            </button>\n                          )}\n                        </td>\n                      </tr>\n                    ))\n                  ) : (\n                    <tr>\n                      <td colSpan=\"5\" className=\"no-data\">\n                        No users found\n                      </td>\n                    </tr>\n                  )}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"dashboard-section\">\n          <h2 className=\"section-title\">Quick Actions</h2>\n          <div className=\"quick-actions\">\n            <button className=\"action-card\">\n              <Users size={24} />\n              <span>Manage Users</span>\n            </button>\n            <button className=\"action-card\">\n              <Heart size={24} />\n              <span>Review Services</span>\n            </button>\n            <button className=\"action-card\">\n              <Activity size={24} />\n              <span>View Reports</span>\n            </button>\n            <button className=\"action-card\">\n              <TrendingUp size={24} />\n              <span>Analytics</span>\n            </button>\n          </div>\n        </div>\n          </>\n        )}\n\n        {/* Manage Users Tab */}\n        {activeTab === 'users' && (\n          <ManageUsers />\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,QAAQ,cAAc;AACnF,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACnC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC;IACjCqB,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE,CAAC;IACjBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd4B,eAAe,CAAC,CAAC;IACjBC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,uCAAuC,EAAE;QACxEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBnB,QAAQ,CAACc,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMV,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,sDAAsD,EAAE;QACvFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBb,cAAc,CAACQ,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;MACpC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,sBAAsB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,UAAU,KAAK;IAC3D,IAAI;MACF,MAAMb,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAM/B,KAAK,CAAC2C,KAAK,CAAC,yCAAyCF,MAAM,SAAS,EACxE;QAAEC;MAAW,CAAC,EACd;QAAER,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAAE,CAClD,CAAC;;MAED;MACAD,gBAAgB,CAAC,CAAC;MAClBD,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDM,KAAK,CAAC,8BAA8B,CAAC;IACvC;EACF,CAAC;EAID,MAAMC,QAAQ,GAAGA,CAAC;IAAEC,IAAI;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAM,CAAC,kBAC7CxC,OAAA;IAAKyC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB1C,OAAA;MAAKyC,SAAS,EAAE,aAAaD,KAAK,EAAG;MAAAE,QAAA,EAClCL;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACN9C,OAAA;MAAKyC,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B1C,OAAA;QAAIyC,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEH;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvC9C,OAAA;QAAGyC,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEJ;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE9C,OAAA;IAAKyC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9B1C,OAAA;MAAKyC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC1C,OAAA;QAAKyC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1C,OAAA;UAAIyC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpD9C,OAAA;UAAGyC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAC,gBAClB,EAACtC,IAAI,CAAC2C,SAAS,EAAC,8CAChC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN9C,OAAA;QAAKyC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB1C,OAAA;UACEyC,SAAS,EAAE,cAAcnC,SAAS,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;UACrE0C,OAAO,EAAEA,CAAA,KAAMzC,YAAY,CAAC,WAAW,CAAE;UAAAmC,QAAA,gBAEzC1C,OAAA,CAACN,QAAQ;YAACuD,IAAI,EAAE;UAAG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9C,OAAA;UACEyC,SAAS,EAAE,cAAcnC,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;UACjE0C,OAAO,EAAEA,CAAA,KAAMzC,YAAY,CAAC,OAAO,CAAE;UAAAmC,QAAA,gBAErC1C,OAAA,CAACR,KAAK;YAACyD,IAAI,EAAE;UAAG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAErB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLxC,SAAS,KAAK,WAAW,iBACxBN,OAAA,CAAAE,SAAA;QAAAwC,QAAA,gBAEE1C,OAAA;UAAKyC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC7B1C,OAAA,CAACoC,QAAQ;YACPC,IAAI,eAAErC,OAAA,CAACR,KAAK;cAACyD,IAAI,EAAE;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BR,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE/B,KAAK,CAACE,UAAW;YACxB8B,KAAK,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACF9C,OAAA,CAACoC,QAAQ;YACPC,IAAI,eAAErC,OAAA,CAACP,KAAK;cAACwD,IAAI,EAAE;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BR,KAAK,EAAC,YAAY;YAClBC,KAAK,EAAE/B,KAAK,CAACG,eAAgB;YAC7B6B,KAAK,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACF9C,OAAA,CAACoC,QAAQ;YACPC,IAAI,eAAErC,OAAA,CAACN,QAAQ;cAACuD,IAAI,EAAE;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BR,KAAK,EAAC,iBAAiB;YACvBC,KAAK,EAAE/B,KAAK,CAACI,cAAe;YAC5B4B,KAAK,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACF9C,OAAA,CAACoC,QAAQ;YACPC,IAAI,eAAErC,OAAA,CAACL,UAAU;cAACsD,IAAI,EAAE;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BR,KAAK,EAAC,oBAAoB;YAC1BC,KAAK,EAAE/B,KAAK,CAACK,iBAAkB;YAC/B2B,KAAK,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN9C,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1C,OAAA;YAAIyC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/C9C,OAAA;YAAKyC,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B1C,OAAA;cAAA0C,QAAA,gBAAG1C,OAAA;gBAAA0C,QAAA,EAAQ;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,yIACc;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,EACL9B,OAAO,gBACNhB,OAAA;YAAKyC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B1C,OAAA;cAAKyC,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvC9C,OAAA;cAAA0C,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,gBAEN9C,OAAA;YAAKyC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B1C,OAAA;cAAOyC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC5B1C,OAAA;gBAAA0C,QAAA,eACE1C,OAAA;kBAAA0C,QAAA,gBACE1C,OAAA;oBAAA0C,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACb9C,OAAA;oBAAA0C,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACd9C,OAAA;oBAAA0C,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACb9C,OAAA;oBAAA0C,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf9C,OAAA;oBAAA0C,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR9C,OAAA;gBAAA0C,QAAA,EACG5B,WAAW,CAACoC,MAAM,GAAG,CAAC,GACrBpC,WAAW,CAACqC,GAAG,CAAC/C,IAAI,iBAClBJ,OAAA;kBAAA0C,QAAA,gBACE1C,OAAA;oBAAIyC,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEtC,IAAI,CAACgD;kBAAI;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1C9C,OAAA;oBAAIyC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEtC,IAAI,CAACiD;kBAAK;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5C9C,OAAA;oBAAA0C,QAAA,eACE1C,OAAA;sBAAMyC,SAAS,EAAE,cAAcrC,IAAI,CAACkD,IAAI,EAAG;sBAAAZ,QAAA,EACxCtC,IAAI,CAACkD;oBAAI;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL9C,OAAA;oBAAA0C,QAAA,eACE1C,OAAA;sBAAMyC,SAAS,EAAE,gBAAgBrC,IAAI,CAACmD,MAAM,EAAG;sBAAAb,QAAA,EAC5CtC,IAAI,CAACmD;oBAAM;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL9C,OAAA;oBAAIyC,SAAS,EAAC,cAAc;oBAAAC,QAAA,EACzBtC,IAAI,CAACmD,MAAM,KAAK,SAAS,gBACxBvD,OAAA;sBACEyC,SAAS,EAAC,oBAAoB;sBAC9BO,OAAO,EAAEA,CAAA,KAAMjB,sBAAsB,CAAC3B,IAAI,CAACoD,EAAE,EAAE,IAAI,CAAE;sBACrDlB,KAAK,EAAC,cAAc;sBAAAI,QAAA,eAEpB1C,OAAA,CAACJ,SAAS;wBAACqD,IAAI,EAAE;sBAAG;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,gBAET9C,OAAA;sBACEyC,SAAS,EAAC,mBAAmB;sBAC7BO,OAAO,EAAEA,CAAA,KAAMjB,sBAAsB,CAAC3B,IAAI,CAACoD,EAAE,EAAE,KAAK,CAAE;sBACtDlB,KAAK,EAAC,cAAc;sBAAAI,QAAA,eAEpB1C,OAAA,CAACH,KAAK;wBAACoD,IAAI,EAAE;sBAAG;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBACT;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GA/BE1C,IAAI,CAACoD,EAAE;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgCZ,CACL,CAAC,gBAEF9C,OAAA;kBAAA0C,QAAA,eACE1C,OAAA;oBAAIyD,OAAO,EAAC,GAAG;oBAAChB,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAEpC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN9C,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1C,OAAA;YAAIyC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChD9C,OAAA;YAAKyC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B1C,OAAA;cAAQyC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC7B1C,OAAA,CAACR,KAAK;gBAACyD,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnB9C,OAAA;gBAAA0C,QAAA,EAAM;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACT9C,OAAA;cAAQyC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC7B1C,OAAA,CAACP,KAAK;gBAACwD,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnB9C,OAAA;gBAAA0C,QAAA,EAAM;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACT9C,OAAA;cAAQyC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC7B1C,OAAA,CAACN,QAAQ;gBAACuD,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB9C,OAAA;gBAAA0C,QAAA,EAAM;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACT9C,OAAA;cAAQyC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC7B1C,OAAA,CAACL,UAAU;gBAACsD,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxB9C,OAAA;gBAAA0C,QAAA,EAAM;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACF,CACH,EAGAxC,SAAS,KAAK,OAAO,iBACpBN,OAAA,CAACF,WAAW;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzC,EAAA,CApPIF,cAAc;AAAAuD,EAAA,GAAdvD,cAAc;AAsPpB,eAAeA,cAAc;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}