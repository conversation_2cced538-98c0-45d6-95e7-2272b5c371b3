const express = require('express');
const { pool } = require('../config/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get admin dashboard statistics
router.get('/stats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Get total users count
    const [totalUsersResult] = await pool.execute(
      'SELECT COUNT(*) as count FROM users WHERE role != ?',
      ['admin']
    );

    // Get total caregivers count
    const [totalCaregiversResult] = await pool.execute(
      'SELECT COUNT(*) as count FROM users WHERE role = ?',
      ['caregiver']
    );

    // Get active service requests count
    const [activeRequestsResult] = await pool.execute(
      'SELECT COUNT(*) as count FROM service_requests WHERE status = ?',
      ['pending']
    );

    // Get completed services count
    const [completedServicesResult] = await pool.execute(
      'SELECT COUNT(*) as count FROM service_requests WHERE status = ?',
      ['completed']
    );

    // Get total applications count
    const [totalApplicationsResult] = await pool.execute(
      'SELECT COUNT(*) as count FROM service_applications'
    );

    res.json({
      success: true,
      data: {
        totalUsers: totalUsersResult[0].count,
        totalCaregivers: totalCaregiversResult[0].count,
        activeRequests: activeRequestsResult[0].count,
        completedServices: completedServicesResult[0].count,
        totalApplications: totalApplicationsResult[0].count
      }
    });
  } catch (error) {
    console.error('Get admin stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get recent users
router.get('/recent-users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const [users] = await pool.execute(
      `SELECT 
        id, 
        email, 
        first_name, 
        last_name, 
        role, 
        is_verified,
        created_at 
       FROM users 
       WHERE role != 'admin'
       ORDER BY created_at DESC 
       LIMIT ?`,
      [parseInt(limit)]
    );

    res.json({
      success: true,
      data: users.map(user => ({
        id: user.id,
        name: `${user.first_name} ${user.last_name}`,
        email: user.email,
        role: user.role,
        status: user.is_verified ? 'active' : 'pending',
        createdAt: user.created_at
      }))
    });
  } catch (error) {
    console.error('Get recent users error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get all users with pagination
router.get('/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 20, role, search } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        id, 
        email, 
        first_name, 
        last_name, 
        role, 
        is_verified,
        created_at 
      FROM users 
      WHERE role != 'admin'
    `;
    
    const params = [];

    if (role && role !== 'all') {
      query += ' AND role = ?';
      params.push(role);
    }

    if (search) {
      query += ' AND (first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const [users] = await pool.execute(query, params);

    // Get total count
    let countQuery = 'SELECT COUNT(*) as total FROM users WHERE role != ?';
    const countParams = ['admin'];

    if (role && role !== 'all') {
      countQuery += ' AND role = ?';
      countParams.push(role);
    }

    if (search) {
      countQuery += ' AND (first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)';
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    const [countResult] = await pool.execute(countQuery, countParams);
    const total = countResult[0].total;

    res.json({
      success: true,
      data: {
        users: users.map(user => ({
          id: user.id,
          name: `${user.first_name} ${user.last_name}`,
          email: user.email,
          role: user.role,
          status: user.is_verified ? 'active' : 'pending',
          createdAt: user.created_at
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update user status (verify/unverify)
router.patch('/users/:id/status', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { isVerified } = req.body;

    await pool.execute(
      'UPDATE users SET is_verified = ? WHERE id = ? AND role != ?',
      [isVerified, id, 'admin']
    );

    res.json({
      success: true,
      message: 'User status updated successfully'
    });
  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get service requests overview
router.get('/service-requests', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 20, status } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        sr.*,
        sc.name as category_name,
        u.first_name as client_first_name,
        u.last_name as client_last_name,
        u.email as client_email,
        (SELECT COUNT(*) FROM service_applications sa WHERE sa.request_id = sr.id) as applications_count
      FROM service_requests sr
      JOIN service_categories sc ON sr.category_id = sc.id
      JOIN users u ON sr.client_id = u.id
    `;
    
    const params = [];

    if (status && status !== 'all') {
      query += ' WHERE sr.status = ?';
      params.push(status);
    }

    query += ' ORDER BY sr.created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const [requests] = await pool.execute(query, params);

    res.json({
      success: true,
      data: requests.map(request => ({
        id: request.id,
        title: request.title,
        category: request.category_name,
        clientName: `${request.client_first_name} ${request.client_last_name}`,
        clientEmail: request.client_email,
        status: request.status,
        hourlyRate: request.hourly_rate,
        applicationsCount: request.applications_count,
        createdAt: request.created_at
      }))
    });
  } catch (error) {
    console.error('Get service requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
