{"name": "companion-backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup-db": "node setup-db.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["companion", "care", "marketplace"], "author": "", "license": "ISC", "description": "Backend API for Companion Care Marketplace", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1"}, "devDependencies": {"nodemon": "^3.1.10"}}