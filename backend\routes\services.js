const express = require('express');
const { pool } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get all service requests (with pagination and filters)
router.get('/requests', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, category, search, status = 'pending' } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        sr.*,
        sc.name as category_name,
        u.first_name as client_first_name,
        u.last_name as client_last_name,
        u.email as client_email,
        (SELECT COUNT(*) FROM service_applications sa WHERE sa.request_id = sr.id) as applications_count
      FROM service_requests sr
      JOIN service_categories sc ON sr.category_id = sc.id
      JOIN users u ON sr.client_id = u.id
      WHERE sr.status = ?
    `;
    
    const params = [status];

    if (category && category !== 'all') {
      query += ' AND sc.name = ?';
      params.push(category);
    }

    if (search) {
      query += ' AND (sr.title LIKE ? OR sr.description LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    query += ' ORDER BY sr.created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const [requests] = await pool.execute(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM service_requests sr
      JOIN service_categories sc ON sr.category_id = sc.id
      WHERE sr.status = ?
    `;
    
    const countParams = [status];
    
    if (category && category !== 'all') {
      countQuery += ' AND sc.name = ?';
      countParams.push(category);
    }

    if (search) {
      countQuery += ' AND (sr.title LIKE ? OR sr.description LIKE ?)';
      countParams.push(`%${search}%`, `%${search}%`);
    }

    const [countResult] = await pool.execute(countQuery, countParams);
    const total = countResult[0].total;

    res.json({
      success: true,
      data: {
        requests: requests.map(request => ({
          id: request.id,
          title: request.title,
          description: request.description,
          category: request.category_name,
          location: request.location,
          hourlyRate: request.hourly_rate,
          startDate: request.start_date,
          endDate: request.end_date,
          startTime: request.start_time,
          endTime: request.end_time,
          status: request.status,
          clientName: `${request.client_first_name} ${request.client_last_name}`,
          clientEmail: request.client_email,
          applicationsCount: request.applications_count,
          createdAt: request.created_at
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get service requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Create a new service request
router.post('/requests', authenticateToken, async (req, res) => {
  try {
    const {
      title,
      description,
      categoryId,
      location,
      hourlyRate,
      startDate,
      endDate,
      startTime,
      endTime,
      specialRequirements
    } = req.body;

    // Validate required fields
    if (!title || !description || !categoryId || !location || !startDate) {
      return res.status(400).json({
        success: false,
        message: 'Title, description, category, location, and start date are required'
      });
    }

    // Only users can create service requests
    if (req.user.role !== 'user') {
      return res.status(403).json({
        success: false,
        message: 'Only users can create service requests'
      });
    }

    const [result] = await pool.execute(
      `INSERT INTO service_requests 
       (client_id, category_id, title, description, location, hourly_rate, 
        start_date, end_date, start_time, end_time, special_requirements) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        req.user.id,
        categoryId,
        title,
        description,
        location,
        hourlyRate || null,
        startDate,
        endDate || null,
        startTime || null,
        endTime || null,
        specialRequirements || null
      ]
    );

    res.status(201).json({
      success: true,
      message: 'Service request created successfully',
      data: {
        requestId: result.insertId
      }
    });
  } catch (error) {
    console.error('Create service request error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get user's own service requests
router.get('/my-requests', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'user') {
      return res.status(403).json({
        success: false,
        message: 'Only users can view their service requests'
      });
    }

    const [requests] = await pool.execute(
      `SELECT 
        sr.*,
        sc.name as category_name,
        (SELECT COUNT(*) FROM service_applications sa WHERE sa.request_id = sr.id) as applications_count
       FROM service_requests sr
       JOIN service_categories sc ON sr.category_id = sc.id
       WHERE sr.client_id = ?
       ORDER BY sr.created_at DESC`,
      [req.user.id]
    );

    res.json({
      success: true,
      data: requests.map(request => ({
        id: request.id,
        title: request.title,
        description: request.description,
        category: request.category_name,
        location: request.location,
        hourlyRate: request.hourly_rate,
        startDate: request.start_date,
        endDate: request.end_date,
        startTime: request.start_time,
        endTime: request.end_time,
        status: request.status,
        applicationsCount: request.applications_count,
        createdAt: request.created_at
      }))
    });
  } catch (error) {
    console.error('Get my requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Apply for a service request
router.post('/requests/:id/apply', authenticateToken, async (req, res) => {
  try {
    const { id: requestId } = req.params;
    const { message, proposedRate } = req.body;

    // Only caregivers can apply
    if (req.user.role !== 'caregiver') {
      return res.status(403).json({
        success: false,
        message: 'Only caregivers can apply for service requests'
      });
    }

    // Check if request exists and is still pending
    const [requests] = await pool.execute(
      'SELECT * FROM service_requests WHERE id = ? AND status = ?',
      [requestId, 'pending']
    );

    if (requests.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Service request not found or no longer available'
      });
    }

    // Check if already applied
    const [existingApplications] = await pool.execute(
      'SELECT id FROM service_applications WHERE request_id = ? AND caregiver_id = ?',
      [requestId, req.user.id]
    );

    if (existingApplications.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'You have already applied for this service request'
      });
    }

    // Create application
    await pool.execute(
      'INSERT INTO service_applications (request_id, caregiver_id, message, proposed_rate) VALUES (?, ?, ?, ?)',
      [requestId, req.user.id, message || null, proposedRate || null]
    );

    res.status(201).json({
      success: true,
      message: 'Application submitted successfully'
    });
  } catch (error) {
    console.error('Apply for service error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get caregiver's applications
router.get('/my-applications', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'caregiver') {
      return res.status(403).json({
        success: false,
        message: 'Only caregivers can view their applications'
      });
    }

    const [applications] = await pool.execute(
      `SELECT 
        sa.*,
        sr.title,
        sr.description,
        sr.location,
        sr.hourly_rate,
        sr.start_date,
        sr.end_date,
        sr.status as request_status,
        sc.name as category_name,
        u.first_name as client_first_name,
        u.last_name as client_last_name
       FROM service_applications sa
       JOIN service_requests sr ON sa.request_id = sr.id
       JOIN service_categories sc ON sr.category_id = sc.id
       JOIN users u ON sr.client_id = u.id
       WHERE sa.caregiver_id = ?
       ORDER BY sa.applied_at DESC`,
      [req.user.id]
    );

    res.json({
      success: true,
      data: applications.map(app => ({
        id: app.id,
        requestId: app.request_id,
        title: app.title,
        description: app.description,
        category: app.category_name,
        location: app.location,
        hourlyRate: app.hourly_rate,
        startDate: app.start_date,
        endDate: app.end_date,
        clientName: `${app.client_first_name} ${app.client_last_name}`,
        message: app.message,
        proposedRate: app.proposed_rate,
        status: app.status,
        requestStatus: app.request_status,
        appliedAt: app.applied_at
      }))
    });
  } catch (error) {
    console.error('Get my applications error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get service categories
router.get('/categories', async (req, res) => {
  try {
    const [categories] = await pool.execute(
      'SELECT * FROM service_categories ORDER BY name'
    );

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
