[{"D:\\Projects\\Companion\\frontend\\src\\index.js": "1", "D:\\Projects\\Companion\\frontend\\src\\reportWebVitals.js": "2", "D:\\Projects\\Companion\\frontend\\src\\App.js": "3", "D:\\Projects\\Companion\\frontend\\src\\components\\Login.js": "4", "D:\\Projects\\Companion\\frontend\\src\\components\\LandingPage.js": "5", "D:\\Projects\\Companion\\frontend\\src\\components\\Signup.js": "6", "D:\\Projects\\Companion\\frontend\\src\\components\\Navbar.js": "7", "D:\\Projects\\Companion\\frontend\\src\\components\\AdminDashboard.js": "8", "D:\\Projects\\Companion\\frontend\\src\\components\\AdminLogin.js": "9"}, {"size": 535, "mtime": 1751101756726, "results": "10", "hashOfConfig": "11"}, {"size": 362, "mtime": 1751101756868, "results": "12", "hashOfConfig": "11"}, {"size": 2340, "mtime": 1751122415572, "results": "13", "hashOfConfig": "11"}, {"size": 3864, "mtime": 1751102171974, "results": "14", "hashOfConfig": "11"}, {"size": 5199, "mtime": 1751122444955, "results": "15", "hashOfConfig": "11"}, {"size": 8036, "mtime": 1751102203721, "results": "16", "hashOfConfig": "11"}, {"size": 1915, "mtime": 1751122011882, "results": "17", "hashOfConfig": "11"}, {"size": 4884, "mtime": 1751102265622, "results": "18", "hashOfConfig": "11"}, {"size": 4340, "mtime": 1751122374829, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sss8xg", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\Companion\\frontend\\src\\index.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\App.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Login.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\LandingPage.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Signup.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Navbar.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\AdminDashboard.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\AdminLogin.js", [], []]