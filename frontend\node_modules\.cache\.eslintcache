[{"D:\\Projects\\Companion\\frontend\\src\\index.js": "1", "D:\\Projects\\Companion\\frontend\\src\\reportWebVitals.js": "2", "D:\\Projects\\Companion\\frontend\\src\\App.js": "3", "D:\\Projects\\Companion\\frontend\\src\\components\\Login.js": "4", "D:\\Projects\\Companion\\frontend\\src\\components\\LandingPage.js": "5", "D:\\Projects\\Companion\\frontend\\src\\components\\Signup.js": "6", "D:\\Projects\\Companion\\frontend\\src\\components\\Navbar.js": "7", "D:\\Projects\\Companion\\frontend\\src\\components\\AdminDashboard.js": "8", "D:\\Projects\\Companion\\frontend\\src\\components\\AdminLogin.js": "9", "D:\\Projects\\Companion\\frontend\\src\\components\\UserDashboard.js": "10", "D:\\Projects\\Companion\\frontend\\src\\components\\CreateServiceRequest.js": "11", "D:\\Projects\\Companion\\frontend\\src\\components\\ManageApplications.js": "12"}, {"size": 535, "mtime": 1751101756726, "results": "13", "hashOfConfig": "14"}, {"size": 362, "mtime": 1751101756868, "results": "15", "hashOfConfig": "14"}, {"size": 2792, "mtime": 1751133213195, "results": "16", "hashOfConfig": "14"}, {"size": 3873, "mtime": 1751133225581, "results": "17", "hashOfConfig": "14"}, {"size": 5199, "mtime": 1751122444955, "results": "18", "hashOfConfig": "14"}, {"size": 8045, "mtime": 1751133236511, "results": "19", "hashOfConfig": "14"}, {"size": 2157, "mtime": 1751133250760, "results": "20", "hashOfConfig": "14"}, {"size": 6961, "mtime": 1751134705732, "results": "21", "hashOfConfig": "14"}, {"size": 4340, "mtime": 1751122374829, "results": "22", "hashOfConfig": "14"}, {"size": 18517, "mtime": 1751134956902, "results": "23", "hashOfConfig": "14"}, {"size": 8452, "mtime": 1751134985147, "results": "24", "hashOfConfig": "14"}, {"size": 6774, "mtime": 1751134793290, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sss8xg", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Projects\\Companion\\frontend\\src\\index.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\App.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Login.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\LandingPage.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Signup.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Navbar.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\AdminDashboard.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\AdminLogin.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\UserDashboard.js", ["62", "63", "64", "65", "66", "67"], [], "D:\\Projects\\Companion\\frontend\\src\\components\\CreateServiceRequest.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\ManageApplications.js", ["68"], [], {"ruleId": "69", "severity": 1, "message": "70", "line": 6, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 6, "endColumn": 9}, {"ruleId": "69", "severity": 1, "message": "73", "line": 9, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 9, "endColumn": 8}, {"ruleId": "69", "severity": 1, "message": "74", "line": 10, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 10, "endColumn": 13}, {"ruleId": "69", "severity": 1, "message": "75", "line": 15, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 15, "endColumn": 14}, {"ruleId": "69", "severity": 1, "message": "76", "line": 16, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 16, "endColumn": 10}, {"ruleId": "77", "severity": 1, "message": "78", "line": 42, "column": 6, "nodeType": "79", "endLine": 42, "endColumn": 47, "suggestions": "80"}, {"ruleId": "77", "severity": 1, "message": "81", "line": 15, "column": 6, "nodeType": "79", "endLine": 15, "endColumn": 25, "suggestions": "82"}, "no-unused-vars", "'Filter' is defined but never used.", "Identifier", "unusedVar", "'Clock' is defined but never used.", "'DollarSign' is defined but never used.", "'CheckCircle' is defined but never used.", "'XCircle' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchServiceRequests'. Either include it or remove the dependency array.", "ArrayExpression", ["83"], "React Hook useEffect has a missing dependency: 'fetchApplications'. Either include it or remove the dependency array.", ["84"], {"desc": "85", "fix": "86"}, {"desc": "87", "fix": "88"}, "Update the dependencies array to be: [user.role, selectedCategory, searchTerm, fetchServiceRequests]", {"range": "89", "text": "90"}, "Update the dependencies array to be: [fetchApplications, isOpen, requestId]", {"range": "91", "text": "92"}, [1245, 1286], "[user.role, selectedCategory, searchTerm, fetchServiceRequests]", [523, 542], "[fetchApplications, isOpen, requestId]"]