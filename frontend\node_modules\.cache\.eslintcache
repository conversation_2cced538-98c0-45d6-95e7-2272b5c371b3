[{"D:\\Projects\\Companion\\frontend\\src\\index.js": "1", "D:\\Projects\\Companion\\frontend\\src\\reportWebVitals.js": "2", "D:\\Projects\\Companion\\frontend\\src\\App.js": "3", "D:\\Projects\\Companion\\frontend\\src\\components\\Login.js": "4", "D:\\Projects\\Companion\\frontend\\src\\components\\LandingPage.js": "5", "D:\\Projects\\Companion\\frontend\\src\\components\\Signup.js": "6", "D:\\Projects\\Companion\\frontend\\src\\components\\Navbar.js": "7", "D:\\Projects\\Companion\\frontend\\src\\components\\AdminDashboard.js": "8", "D:\\Projects\\Companion\\frontend\\src\\components\\AdminLogin.js": "9", "D:\\Projects\\Companion\\frontend\\src\\components\\UserDashboard.js": "10", "D:\\Projects\\Companion\\frontend\\src\\components\\CreateServiceRequest.js": "11", "D:\\Projects\\Companion\\frontend\\src\\components\\ManageApplications.js": "12", "D:\\Projects\\Companion\\frontend\\src\\components\\ManageUsers.js": "13"}, {"size": 535, "mtime": 1751101756726, "results": "14", "hashOfConfig": "15"}, {"size": 362, "mtime": 1751101756868, "results": "16", "hashOfConfig": "15"}, {"size": 2792, "mtime": 1751133213195, "results": "17", "hashOfConfig": "15"}, {"size": 3873, "mtime": 1751133225581, "results": "18", "hashOfConfig": "15"}, {"size": 5199, "mtime": 1751122444955, "results": "19", "hashOfConfig": "15"}, {"size": 8045, "mtime": 1751133236511, "results": "20", "hashOfConfig": "15"}, {"size": 2157, "mtime": 1751133250760, "results": "21", "hashOfConfig": "15"}, {"size": 8116, "mtime": 1751136591536, "results": "22", "hashOfConfig": "15"}, {"size": 4340, "mtime": 1751122374829, "results": "23", "hashOfConfig": "15"}, {"size": 18517, "mtime": 1751134956902, "results": "24", "hashOfConfig": "15"}, {"size": 8452, "mtime": 1751134985147, "results": "25", "hashOfConfig": "15"}, {"size": 6774, "mtime": 1751134793290, "results": "26", "hashOfConfig": "15"}, {"size": 10458, "mtime": 1751136291105, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sss8xg", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Projects\\Companion\\frontend\\src\\index.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\App.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Login.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\LandingPage.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Signup.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Navbar.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\AdminDashboard.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\AdminLogin.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\UserDashboard.js", ["67", "68", "69", "70", "71", "72"], [], "D:\\Projects\\Companion\\frontend\\src\\components\\CreateServiceRequest.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\ManageApplications.js", ["73"], [], "D:\\Projects\\Companion\\frontend\\src\\components\\ManageUsers.js", ["74", "75"], [], {"ruleId": "76", "severity": 1, "message": "77", "line": 6, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 6, "endColumn": 9}, {"ruleId": "76", "severity": 1, "message": "80", "line": 9, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 9, "endColumn": 8}, {"ruleId": "76", "severity": 1, "message": "81", "line": 10, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 10, "endColumn": 13}, {"ruleId": "76", "severity": 1, "message": "82", "line": 15, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 15, "endColumn": 14}, {"ruleId": "76", "severity": 1, "message": "83", "line": 16, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 16, "endColumn": 10}, {"ruleId": "84", "severity": 1, "message": "85", "line": 42, "column": 6, "nodeType": "86", "endLine": 42, "endColumn": 47, "suggestions": "87"}, {"ruleId": "84", "severity": 1, "message": "88", "line": 15, "column": 6, "nodeType": "86", "endLine": 15, "endColumn": 25, "suggestions": "89"}, {"ruleId": "76", "severity": 1, "message": "77", "line": 4, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 4, "endColumn": 9}, {"ruleId": "84", "severity": 1, "message": "90", "line": 32, "column": 6, "nodeType": "86", "endLine": 32, "endColumn": 61, "suggestions": "91"}, "no-unused-vars", "'Filter' is defined but never used.", "Identifier", "unusedVar", "'Clock' is defined but never used.", "'DollarSign' is defined but never used.", "'CheckCircle' is defined but never used.", "'XCircle' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchServiceRequests'. Either include it or remove the dependency array.", "ArrayExpression", ["92"], "React Hook useEffect has a missing dependency: 'fetchApplications'. Either include it or remove the dependency array.", ["93"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["94"], {"desc": "95", "fix": "96"}, {"desc": "97", "fix": "98"}, {"desc": "99", "fix": "100"}, "Update the dependencies array to be: [user.role, selectedCategory, searchTerm, fetchServiceRequests]", {"range": "101", "text": "102"}, "Update the dependencies array to be: [fetchApplications, isOpen, requestId]", {"range": "103", "text": "104"}, "Update the dependencies array to be: [fetchUsers, pagination.page, roleFilter, searchTerm, statusFilter]", {"range": "105", "text": "106"}, [1245, 1286], "[user.role, selectedCategory, searchTerm, fetchServiceRequests]", [523, 542], "[fetchApplications, isOpen, requestId]", [684, 739], "[fetchUsers, pagination.page, roleFilter, searchTerm, statusFilter]"]