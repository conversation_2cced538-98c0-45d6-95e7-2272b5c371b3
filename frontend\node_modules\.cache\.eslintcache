[{"D:\\Projects\\Companion\\frontend\\src\\index.js": "1", "D:\\Projects\\Companion\\frontend\\src\\reportWebVitals.js": "2", "D:\\Projects\\Companion\\frontend\\src\\App.js": "3", "D:\\Projects\\Companion\\frontend\\src\\components\\Login.js": "4", "D:\\Projects\\Companion\\frontend\\src\\components\\LandingPage.js": "5", "D:\\Projects\\Companion\\frontend\\src\\components\\Signup.js": "6", "D:\\Projects\\Companion\\frontend\\src\\components\\Navbar.js": "7", "D:\\Projects\\Companion\\frontend\\src\\components\\AdminDashboard.js": "8", "D:\\Projects\\Companion\\frontend\\src\\components\\AdminLogin.js": "9", "D:\\Projects\\Companion\\frontend\\src\\components\\UserDashboard.js": "10", "D:\\Projects\\Companion\\frontend\\src\\components\\CreateServiceRequest.js": "11"}, {"size": 535, "mtime": 1751101756726, "results": "12", "hashOfConfig": "13"}, {"size": 362, "mtime": 1751101756868, "results": "14", "hashOfConfig": "13"}, {"size": 2792, "mtime": 1751133213195, "results": "15", "hashOfConfig": "13"}, {"size": 3873, "mtime": 1751133225581, "results": "16", "hashOfConfig": "13"}, {"size": 5199, "mtime": 1751122444955, "results": "17", "hashOfConfig": "13"}, {"size": 8045, "mtime": 1751133236511, "results": "18", "hashOfConfig": "13"}, {"size": 2157, "mtime": 1751133250760, "results": "19", "hashOfConfig": "13"}, {"size": 4884, "mtime": 1751102265622, "results": "20", "hashOfConfig": "13"}, {"size": 4340, "mtime": 1751122374829, "results": "21", "hashOfConfig": "13"}, {"size": 15495, "mtime": 1751133560083, "results": "22", "hashOfConfig": "13"}, {"size": 8452, "mtime": 1751133476276, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sss8xg", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\Companion\\frontend\\src\\index.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\App.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Login.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\LandingPage.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Signup.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\Navbar.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\AdminDashboard.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\AdminLogin.js", [], [], "D:\\Projects\\Companion\\frontend\\src\\components\\UserDashboard.js", ["57", "58", "59", "60", "61"], [], "D:\\Projects\\Companion\\frontend\\src\\components\\CreateServiceRequest.js", [], [], {"ruleId": "62", "severity": 1, "message": "63", "line": 6, "column": 3, "nodeType": "64", "messageId": "65", "endLine": 6, "endColumn": 9}, {"ruleId": "62", "severity": 1, "message": "66", "line": 9, "column": 3, "nodeType": "64", "messageId": "65", "endLine": 9, "endColumn": 8}, {"ruleId": "62", "severity": 1, "message": "67", "line": 15, "column": 3, "nodeType": "64", "messageId": "65", "endLine": 15, "endColumn": 14}, {"ruleId": "62", "severity": 1, "message": "68", "line": 16, "column": 3, "nodeType": "64", "messageId": "65", "endLine": 16, "endColumn": 10}, {"ruleId": "69", "severity": 1, "message": "70", "line": 39, "column": 6, "nodeType": "71", "endLine": 39, "endColumn": 47, "suggestions": "72"}, "no-unused-vars", "'Filter' is defined but never used.", "Identifier", "unusedVar", "'Clock' is defined but never used.", "'CheckCircle' is defined but never used.", "'XCircle' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchServiceRequests'. Either include it or remove the dependency array.", "ArrayExpression", ["73"], {"desc": "74", "fix": "75"}, "Update the dependencies array to be: [user.role, selectedCategory, searchTerm, fetchServiceRequests]", {"range": "76", "text": "77"}, [1049, 1090], "[user.role, selectedCategory, searchTerm, fetchServiceRequests]"]